#!/usr/bin/env python3
"""
Test script para verificar que todos los módulos funcionen correctamente
"""
import sys
import os

def test_imports():
    """Probar que todos los módulos se importen correctamente"""
    print("🔍 Probando importaciones...")
    
    try:
        import tkinter as tk
        print("✅ tkinter - OK")
    except ImportError as e:
        print(f"❌ tkinter - Error: {e}")
        return False
    
    try:
        import paramiko
        print("✅ paramiko - OK")
    except ImportError as e:
        print(f"❌ paramiko - Error: {e}")
        return False
    
    try:
        import requests
        print("✅ requests - OK")
    except ImportError as e:
        print(f"❌ requests - Error: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ Pillow - OK")
    except ImportError as e:
        print(f"❌ Pillow - Error: {e}")
        return False
    
    try:
        import urllib3
        print("✅ urllib3 - OK")
    except ImportError as e:
        print(f"❌ urllib3 - Error: {e}")
        return False
    
    return True

def test_modules():
    """Probar que nuestros módulos se importen correctamente"""
    print("\n🔍 Probando módulos principales...")
    
    try:
        from m3u_parser import M3UParser, M3UItem, ContentType
        print("✅ m3u_parser - OK")
    except ImportError as e:
        print(f"❌ m3u_parser - Error: {e}")
        return False
    
    try:
        from ssh_manager import SSHManager, RemoteFile, FileType
        print("✅ ssh_manager - OK")
    except ImportError as e:
        print(f"❌ ssh_manager - Error: {e}")
        return False
    
    try:
        from download_manager import DownloadManager, DownloadTask, DownloadStatus
        print("✅ download_manager - OK")
    except ImportError as e:
        print(f"❌ download_manager - Error: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Probar funcionalidad básica"""
    print("\n🔍 Probando funcionalidad básica...")
    
    try:
        # Test M3U Parser
        from m3u_parser import M3UParser
        parser = M3UParser()
        print("✅ M3UParser instanciado correctamente")
    except Exception as e:
        print(f"❌ M3UParser - Error: {e}")
        return False
    
    try:
        # Test SSH Manager
        from ssh_manager import SSHManager
        ssh = SSHManager()
        print("✅ SSHManager instanciado correctamente")
    except Exception as e:
        print(f"❌ SSHManager - Error: {e}")
        return False
    
    try:
        # Test Download Manager
        from download_manager import DownloadManager
        dm = DownloadManager()
        print("✅ DownloadManager instanciado correctamente")
    except Exception as e:
        print(f"❌ DownloadManager - Error: {e}")
        return False
    
    return True

def test_tkinter_window():
    """Probar que se puede crear una ventana Tkinter"""
    print("\n🔍 Probando interfaz gráfica...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Crear ventana de prueba
        root = tk.Tk()
        root.title("Test Window")
        root.geometry("300x200")
        
        # Crear algunos widgets
        label = tk.Label(root, text="Test funcionando ✅")
        label.pack(pady=20)
        
        button = tk.Button(root, text="Cerrar", command=root.quit)
        button.pack(pady=10)
        
        # No mostrar la ventana, solo verificar que se puede crear
        root.withdraw()
        root.destroy()
        
        print("✅ Interfaz gráfica - OK")
        return True
        
    except Exception as e:
        print(f"❌ Interfaz gráfica - Error: {e}")
        return False

def main():
    """Función principal de prueba"""
    print("=" * 60)
    print("🧪 M3U Parser Pro - Test de Funcionalidad")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Ejecutar pruebas
    if not test_imports():
        all_tests_passed = False
    
    if not test_modules():
        all_tests_passed = False
    
    if not test_basic_functionality():
        all_tests_passed = False
    
    if not test_tkinter_window():
        all_tests_passed = False
    
    # Resultado final
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 ¡TODAS LAS PRUEBAS PASARON!")
        print("✅ La aplicación está lista para usar")
        print("\n💡 Para ejecutar la aplicación:")
        print("   python main.py")
        print("\n📚 Para más información, consulte README.md")
    else:
        print("❌ ALGUNAS PRUEBAS FALLARON")
        print("🔧 Revise los errores anteriores y:")
        print("   - Verifique que todas las dependencias estén instaladas")
        print("   - Ejecute: pip install -r requirements.txt")
        print("   - Consulte README.md para más información")
    
    print("=" * 60)
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
