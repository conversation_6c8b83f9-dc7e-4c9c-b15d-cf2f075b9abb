#!/usr/bin/env python3
"""
Launcher para M3U Parser Pro
"""
import sys
import os
import subprocess

def main():
    """Función principal del launcher"""
    print("🚀 Iniciando M3U Parser Pro...")
    print("=" * 50)
    
    # Verificar que estamos en el directorio correcto
    if not os.path.exists("main.py"):
        print("❌ Error: No se encuentra main.py")
        print("🔧 Asegúrese de ejecutar desde el directorio del proyecto")
        return False
    
    # Verificar que las dependencias estén instaladas
    try:
        import tkinter
        import paramiko
        import requests
        from PIL import Image
        import urllib3
    except ImportError as e:
        print(f"❌ Error: Dependencia faltante - {e}")
        print("🔧 Ejecute: pip install -r requirements.txt")
        return False
    
    # Verificar que los módulos principales existan
    required_files = ["m3u_parser.py", "ssh_manager.py", "download_manager.py"]
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ Error: Archivo faltante - {file}")
            return False
    
    print("✅ Todos los archivos y dependencias están disponibles")
    print("🎬 Iniciando aplicación...")
    print()
    
    # Ejecutar la aplicación principal
    try:
        from main import main as app_main
        app_main()
        return True
    except Exception as e:
        print(f"❌ Error ejecutando la aplicación: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("Presione Enter para continuar...")
        sys.exit(1)
