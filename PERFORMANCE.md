# M3U Parser Pro - Configuración de Rendimiento

## Configuración del Sistema

### SSH/SFTP Optimizado
- **Conexiones persistentes**: Mantiene la conexión activa con keep-alive
- **Timeout inteligente**: 30 segundos por defecto, ajustable
- **Reconexión automática**: En caso de pérdida de conexión
- **Compresión activada**: Para mejorar velocidad en conexiones lentas

### Descargas Optimizadas
- **Wget configurado**: Usa las mejores opciones para velocidad
- **Reintentos automáticos**: 3 intentos por defecto
- **Descargas concurrentes**: Hasta 3 simultáneas
- **Gestión de memoria**: Bajo uso de RAM (solo metadatos)

### Interfaz Responsiva
- **Threads separados**: UI nunca se bloquea
- **Actualizaciones periódicas**: Cada 2 segundos
- **Callbacks asíncronos**: Para mejor rendimiento
- **Logging optimizado**: Solo información esencial

## Configuración Avanzada

### Para Servidores Potentes
```python
# En download_manager.py, línea 45
max_concurrent_downloads = 5  # Incrementar a 5

# En ssh_manager.py, línea 85
timeout = 60  # Incrementar timeout
```

### Para Conexiones Lentas
```python
# En download_manager.py, línea 276
timeout = 120  # Incrementar timeout de wget

# En ssh_manager.py, línea 60
timeout = 60  # Incrementar timeout SSH
```

### Para Muchos Archivos
```python
# En main.py, línea 99
self.root.after(5000, self.monitor_downloads)  # Reducir frecuencia
```

## Optimizaciones del Sistema

### Windows
- Usar SSD para mejor rendimiento
- Tener al menos 4GB RAM disponibles
- Conexión estable a Internet

### Linux/Ubuntu Server
- Instalar wget optimizado: `sudo apt install wget`
- Configurar límites de archivo: `ulimit -n 65536`
- Usar filesystem eficiente (ext4, xfs)

### Red
- Usar conexión por cable cuando sea posible
- Configurar QoS para priorizar SSH
- Monitorear ancho de banda del servidor

## Monitoreo de Rendimiento

### Métricas Importantes
- **Velocidad de descarga**: Mostrada en interfaz
- **Tiempo de respuesta SSH**: < 1 segundo ideal
- **Uso de memoria**: < 100MB típico
- **Descargas completadas**: Tasa de éxito > 95%

### Logs de Rendimiento
Revisar `m3u_parser.log` para:
- Tiempos de conexión SSH
- Errores de descarga
- Estadísticas de velocidad
- Problemas de red

## Solución de Problemas de Rendimiento

### Descargas Lentas
1. Verificar ancho de banda del servidor
2. Probar con menos descargas concurrentes
3. Verificar latencia de red
4. Revisar carga del servidor

### Conexiones SSH Inestables
1. Aumentar timeout de conexión
2. Verificar firewall del servidor
3. Probar con compresión desactivada
4. Revisar logs del servidor SSH

### Interfaz Lenta
1. Reducir frecuencia de actualización
2. Cerrar aplicaciones innecesarias
3. Verificar uso de memoria
4. Reiniciar aplicación

## Configuración Óptima por Escenario

### Uso Doméstico
```json
{
    "downloads": {
        "max_concurrent": 2,
        "timeout": 60
    },
    "ssh": {
        "timeout": 30
    }
}
```

### Servidor Dedicado
```json
{
    "downloads": {
        "max_concurrent": 5,
        "timeout": 120
    },
    "ssh": {
        "timeout": 60
    }
}
```

### Conexión Móvil
```json
{
    "downloads": {
        "max_concurrent": 1,
        "timeout": 180
    },
    "ssh": {
        "timeout": 90
    }
}
```
