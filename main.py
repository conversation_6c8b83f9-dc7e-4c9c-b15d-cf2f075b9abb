import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import threading
import queue
import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

# Importar nuestros módulos
from m3u_parser import M3UParser, M3<PERSON><PERSON>, ContentType
from ssh_manager import SSHManager, RemoteFile, FileType
from download_manager import DownloadManager, DownloadTask, DownloadStatus, SeriesDownloadManager

# Configurar logging mejorado para debug
def setup_logging():
    """Configurar sistema de logging avanzado"""
    # Crear logger principal
    logger = logging.getLogger('M3UParserApp')
    logger.setLevel(logging.DEBUG)
    
    # Crear formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    # Handler para archivo (DEBUG)
    file_handler = logging.FileHandler('m3u_parser_debug.log', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    
    # Handler para consola (INFO)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # Agregar handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# Configurar logging
logger = setup_logging()

class M3UParserApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🎬 M3U Parser Pro - Descarga Remota")
        self.root.geometry("1600x1000")  # Tamaño más grande
        self.root.configure(bg='#1e1e1e')  # Fondo más oscuro
        
        # Logger para esta instancia
        self.logger = logging.getLogger('M3UParserApp')
        self.logger.info("=== INICIANDO M3U PARSER PRO ===")
        
        # Control de threads
        self.active_threads = []
        self.thread_lock = threading.Lock()
        
        # Configurar icono de ventana si existe
        try:
            # Intentar configurar un icono personalizado
            self.root.iconbitmap(default='icon.ico') if os.path.exists('icon.ico') else None
            self.logger.debug("Icono de ventana configurado")
        except Exception as e:
            self.logger.warning(f"No se pudo configurar icono: {e}")
        
        # Managers principales
        self.logger.debug("Inicializando managers...")
        self.m3u_parser = M3UParser()
        self.ssh_manager = SSHManager()
        self.download_manager = DownloadManager()
        self.series_manager = SeriesDownloadManager(self.download_manager)
        
        # Variables principales
        self.m3u_content = []
        self.selected_items = []
        self.current_remote_path = ""
        self.destination_folder = ""
        
        # Configurar callbacks
        self.download_manager.set_progress_callback(self.on_download_progress)
        self.download_manager.set_status_callback(self.on_download_status)
        self.logger.debug("Callbacks configurados")
        
        # Configurar el estilo
        self.logger.debug("Configurando estilos...")
        self.setup_styles()
        
        # Crear la interfaz
        self.logger.debug("Creando interfaz...")
        self.create_interface()
        
        # Inicializar el sistema de monitoreo
        self.logger.debug("Iniciando sistema de monitoreo...")
        self.setup_monitoring()
        
        # Configurar cierre de aplicación
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.logger.info("Aplicación M3U Parser Pro iniciada exitosamente")
    
    def setup_monitoring(self):
        """Configurar sistema de monitoreo"""
        self.logger.debug("Configurando sistema de monitoreo...")
        self.monitor_downloads()
        
        # Monitoreo de threads cada 30 segundos
        self.monitor_threads()
    
    def monitor_threads(self):
        """Monitorear threads activos"""
        try:
            self.cleanup_threads()
            
            # Log cada 30 segundos
            if len(self.active_threads) > 0:
                self.logger.debug(f"Monitoreo threads: {len(self.active_threads)} activos")
                for thread in self.active_threads:
                    self.logger.debug(f"  - {thread.name}: {thread.is_alive()}")
            
            # Programar siguiente monitoreo
            self.root.after(30000, self.monitor_threads)
        except Exception as e:
            self.logger.error(f"Error monitoreando threads: {e}")
            self.root.after(30000, self.monitor_threads)
    
    def on_download_progress(self, message: str):
        """Callback de progreso de descarga"""
        self.logger.debug(f"Progreso descarga: {message}")
        self.progress_label.config(text=message)
    
    def on_download_status(self, event: str, task: Optional[DownloadTask]):
        """Callback de estado de descarga"""
        self.logger.debug(f"Estado descarga: {event} - {task.filename if task else 'None'}")
        
        if event == "download_started" and task:
            self.progress_label.config(text=f"Iniciando: {task.filename}")
        elif event == "download_completed" and task:
            self.progress_label.config(text=f"Completado: {task.filename}")
        elif event == "download_failed" and task:
            self.progress_label.config(text=f"Error: {task.filename}")
        
        # Actualizar estadísticas
        self.update_download_stats()
    
    def update_download_stats(self):
        """Actualizar estadísticas de descarga"""
        try:
            stats = self.download_manager.get_download_stats()
            queue_status = self.download_manager.get_queue_status()
            
            self.logger.debug(f"Estadísticas: {stats}")
            self.logger.debug(f"Cola: {queue_status}")
            
            self.queue_label.config(text=f"Cola: {queue_status['pending']} | Activas: {queue_status['active']}")
            self.download_label.config(text=f"Completadas: {stats['completed_downloads']} | Fallidas: {stats['failed_downloads']}")
            
            # Actualizar barra de progreso si hay descargas activas
            if queue_status['active'] > 0:
                total = queue_status['total']
                completed = stats['completed_downloads']
                if total > 0:
                    progress = (completed / total) * 100
                    self.progress_var.set(progress)
                    self.logger.debug(f"Progreso: {progress:.1f}%")
            else:
                self.progress_var.set(0)
                
        except Exception as e:
            self.logger.error(f"Error actualizando estadísticas: {e}")
    
    def monitor_downloads(self):
        """Monitorear las descargas"""
        try:
            self.update_download_stats()
            # Programar siguiente actualización
            self.root.after(2000, self.monitor_downloads)
        except Exception as e:
            self.logger.error(f"Error monitoreando descargas: {e}")
            self.root.after(5000, self.monitor_downloads)
    
    def setup_styles(self):
        """Configurar estilos para una apariencia profesional"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Colores profesionales mejorados
        style.configure('Main.TFrame', background='#1e1e1e', relief='flat')
        style.configure('Panel.TFrame', background='#2d2d2d', relief='groove', borderwidth=2)
        style.configure('Header.TLabel', 
                       background='#1e1e1e', 
                       foreground='#ffffff', 
                       font=('Segoe UI', 14, 'bold'))
        style.configure('Status.TLabel', 
                       background='#2d2d2d', 
                       foreground='#00ff88', 
                       font=('Consolas', 10))
        style.configure('Action.TButton', 
                       font=('Segoe UI', 10, 'bold'),
                       padding=(10, 5))
        
        # Configurar treeview con mejores colores
        style.configure('M3U.Treeview', 
                       background='#333333', 
                       foreground='#ffffff', 
                       fieldbackground='#333333',
                       borderwidth=1,
                       relief='solid')
        style.configure('M3U.Treeview.Heading', 
                       background='#404040', 
                       foreground='#ffffff', 
                       font=('Segoe UI', 10, 'bold'),
                       relief='raised',
                       borderwidth=1)
        
        # Configurar Entry fields
        style.configure('TEntry',
                       fieldbackground='#404040',
                       foreground='#ffffff',
                       borderwidth=1,
                       relief='solid')
        
        # Configurar LabelFrame
        style.configure('TLabelframe',
                       background='#2d2d2d',
                       foreground='#ffffff',
                       borderwidth=2,
                       relief='groove')
        style.configure('TLabelframe.Label',
                       background='#2d2d2d',
                       foreground='#00ccff',
                       font=('Segoe UI', 11, 'bold'))
        
        # Configurar Progressbar
        style.configure('TProgressbar',
                       background='#00ff88',
                       troughcolor='#404040',
                       borderwidth=1,
                       relief='solid')
        
    def create_interface(self):
        """Crear la interfaz principal"""
        # Frame principal
        main_frame = ttk.Frame(self.root, style='Main.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Panel superior - Controles
        self.create_control_panel(main_frame)
        
        # Panel central - Contenido dividido
        self.create_content_panels(main_frame)
        
        # Panel inferior - Estado y progreso
        self.create_status_panel(main_frame)
    
    def create_control_panel(self, parent):
        """Crear panel de controles superior"""
        control_frame = ttk.Frame(parent, style='Panel.TFrame')
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Título principal mejorado
        title_frame = ttk.Frame(control_frame, style='Panel.TFrame')
        title_frame.pack(fill=tk.X, pady=15)
        
        title_label = ttk.Label(title_frame, 
                               text="🎬 M3U Parser Pro - Descarga Remota SSH/SFTP", 
                               style='Header.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, 
                                  text="Precisión de reloj suizo • Poder de león", 
                                  background='#2d2d2d', 
                                  foreground='#888888', 
                                  font=('Segoe UI', 9, 'italic'))
        subtitle_label.pack(pady=(5, 0))
        
        # Separador visual
        separator = ttk.Separator(control_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=10)
        
        # Botones principales organizados en grupos
        button_frame = ttk.Frame(control_frame, style='Panel.TFrame')
        button_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # Grupo 1: Carga de contenido
        group1_frame = ttk.LabelFrame(button_frame, text="📂 Contenido M3U", style='TLabelframe')
        group1_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        ttk.Button(group1_frame, text="📁 Cargar Archivo", command=self.load_m3u_file, style='Action.TButton').pack(padx=5, pady=5)
        ttk.Button(group1_frame, text="🌐 Cargar URL", command=self.load_m3u_url, style='Action.TButton').pack(padx=5, pady=5)
        
        # Grupo 2: Conexión SSH
        group2_frame = ttk.LabelFrame(button_frame, text="🖥️ Servidor SSH", style='TLabelframe')
        group2_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        ttk.Button(group2_frame, text="� Conectar SSH", command=self.connect_ssh, style='Action.TButton').pack(padx=5, pady=5)
        ttk.Button(group2_frame, text="🔌 Desconectar", command=self.disconnect_ssh, style='Action.TButton').pack(padx=5, pady=5)
        
        # Grupo 3: Control de descargas
        group3_frame = ttk.LabelFrame(button_frame, text="⬇️ Descargas", style='TLabelframe')
        group3_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        ttk.Button(group3_frame, text="▶️ Iniciar", command=self.start_downloads, style='Action.TButton').pack(padx=5, pady=2)
        ttk.Button(group3_frame, text="⏹️ Detener", command=self.stop_downloads, style='Action.TButton').pack(padx=5, pady=2)
        ttk.Button(group3_frame, text="🗑️ Limpiar", command=self.clear_queue, style='Action.TButton').pack(padx=5, pady=2)
    
    def create_content_panels(self, parent):
        """Crear paneles de contenido principal"""
        content_frame = ttk.Frame(parent, style='Main.TFrame')
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Panel izquierdo - M3U Content
        self.create_m3u_panel(content_frame)
        
        # Panel derecho - SSH/SFTP
        self.create_ssh_panel(content_frame)
    
    def create_m3u_panel(self, parent):
        """Crear panel de contenido M3U"""
        # Frame del panel M3U
        m3u_frame = ttk.LabelFrame(parent, text="📺 Contenido M3U", style='Panel.TFrame')
        m3u_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Crear Treeview para mostrar el contenido
        columns = ('type', 'name', 'group', 'url', 'status')
        self.m3u_tree = ttk.Treeview(m3u_frame, columns=columns, show='tree headings', style='M3U.Treeview')
        
        # Configurar columnas
        self.m3u_tree.heading('#0', text='📁')
        self.m3u_tree.heading('type', text='Tipo')
        self.m3u_tree.heading('name', text='Nombre')
        self.m3u_tree.heading('group', text='Grupo')
        self.m3u_tree.heading('url', text='URL')
        self.m3u_tree.heading('status', text='Estado')
        
        # Ajustar ancho de columnas
        self.m3u_tree.column('#0', width=50)
        self.m3u_tree.column('type', width=80)
        self.m3u_tree.column('name', width=300)
        self.m3u_tree.column('group', width=150)
        self.m3u_tree.column('url', width=200)
        self.m3u_tree.column('status', width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(m3u_frame, orient=tk.VERTICAL, command=self.m3u_tree.yview)
        h_scrollbar = ttk.Scrollbar(m3u_frame, orient=tk.HORIZONTAL, command=self.m3u_tree.xview)
        self.m3u_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Empaquetar
        self.m3u_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Menú contextual
        self.create_context_menu()
        
        # Eventos
        self.m3u_tree.bind('<Button-3>', self.show_context_menu)
        self.m3u_tree.bind('<Double-1>', self.on_item_double_click)
    
    def create_ssh_panel(self, parent):
        """Crear panel SSH/SFTP"""
        # Frame del panel SSH
        ssh_frame = ttk.LabelFrame(parent, text="🖥️ Navegador SSH/SFTP", style='Panel.TFrame')
        ssh_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # Panel de conexión
        conn_frame = ttk.Frame(ssh_frame, style='Panel.TFrame')
        conn_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Configurar grid
        conn_frame.grid_columnconfigure(1, weight=1)
        conn_frame.grid_columnconfigure(3, weight=1)
        
        # Fila 1: Servidor y Usuario
        ttk.Label(conn_frame, text="🖥️ Servidor:", background='#3c3c3c', foreground='#ffffff', font=('Arial', 9, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.server_entry = ttk.Entry(conn_frame, width=20, font=('Arial', 9))
        self.server_entry.grid(row=0, column=1, padx=5, pady=2, sticky=tk.EW)
        
        ttk.Label(conn_frame, text="👤 Usuario:", background='#3c3c3c', foreground='#ffffff', font=('Arial', 9, 'bold')).grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.user_entry = ttk.Entry(conn_frame, width=15, font=('Arial', 9))
        self.user_entry.grid(row=0, column=3, padx=5, pady=2, sticky=tk.EW)
        
        # Fila 2: Puerto y Contraseña
        ttk.Label(conn_frame, text="🔌 Puerto:", background='#3c3c3c', foreground='#ffffff', font=('Arial', 9, 'bold')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.port_entry = ttk.Entry(conn_frame, width=8, font=('Arial', 9))
        self.port_entry.insert(0, "22")
        self.port_entry.grid(row=1, column=1, padx=5, pady=2, sticky=tk.W)
        
        ttk.Label(conn_frame, text="🔒 Contraseña:", background='#3c3c3c', foreground='#ffffff', font=('Arial', 9, 'bold')).grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        self.password_entry = ttk.Entry(conn_frame, width=15, show='*', font=('Arial', 9))
        self.password_entry.grid(row=1, column=3, padx=5, pady=2, sticky=tk.EW)
        
        # Fila 3: Botón de conexión y estado
        button_frame = ttk.Frame(conn_frame, style='Panel.TFrame')
        button_frame.grid(row=2, column=0, columnspan=4, pady=10, sticky=tk.EW)
        
        ttk.Button(button_frame, text="🔗 Conectar SSH/SFTP", command=self.connect_ssh, style='Action.TButton').pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔌 Desconectar", command=self.disconnect_ssh, style='Action.TButton').pack(side=tk.LEFT, padx=5)
        
        # Estado de conexión mejorado
        self.ssh_status = ttk.Label(button_frame, text="❌ Desconectado", style='Status.TLabel', font=('Arial', 10, 'bold'))
        self.ssh_status.pack(side=tk.RIGHT, padx=10)
        
        # Navegador de archivos remoto
        self.create_remote_browser(ssh_frame)
        
        # Panel de destino
        dest_frame = ttk.Frame(ssh_frame, style='Panel.TFrame')
        dest_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(dest_frame, text="📁 Carpeta destino:", background='#3c3c3c', foreground='#ffffff').pack(side=tk.LEFT, padx=5)
        self.dest_label = ttk.Label(dest_frame, text="No seleccionada", background='#3c3c3c', foreground='#ffaa00')
        self.dest_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(dest_frame, text="📌 Fijar como destino", command=self.set_destination_folder).pack(side=tk.RIGHT, padx=5)
    
    def create_remote_browser(self, parent):
        """Crear navegador de archivos remoto"""
        browser_frame = ttk.Frame(parent, style='Panel.TFrame')
        browser_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Barra de herramientas
        toolbar = ttk.Frame(browser_frame, style='Panel.TFrame')
        toolbar.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(toolbar, text="🔄 Actualizar", command=self.refresh_remote_browser).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="📁 Nueva carpeta", command=self.create_remote_folder).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="🗑️ Eliminar", command=self.delete_remote_item).pack(side=tk.LEFT, padx=2)
        
        # Treeview para archivos remotos
        remote_columns = ('name', 'size', 'modified', 'permissions')
        self.remote_tree = ttk.Treeview(browser_frame, columns=remote_columns, show='tree headings', style='M3U.Treeview')
        
        # Configurar columnas
        self.remote_tree.heading('#0', text='📁')
        self.remote_tree.heading('name', text='Nombre')
        self.remote_tree.heading('size', text='Tamaño')
        self.remote_tree.heading('modified', text='Modificado')
        self.remote_tree.heading('permissions', text='Permisos')
        
        # Ajustar ancho de columnas
        self.remote_tree.column('#0', width=50)
        self.remote_tree.column('name', width=200)
        self.remote_tree.column('size', width=80)
        self.remote_tree.column('modified', width=120)
        self.remote_tree.column('permissions', width=80)
        
        # Scrollbars
        remote_v_scrollbar = ttk.Scrollbar(browser_frame, orient=tk.VERTICAL, command=self.remote_tree.yview)
        remote_h_scrollbar = ttk.Scrollbar(browser_frame, orient=tk.HORIZONTAL, command=self.remote_tree.xview)
        self.remote_tree.configure(yscrollcommand=remote_v_scrollbar.set, xscrollcommand=remote_h_scrollbar.set)
        
        # Empaquetar
        self.remote_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        remote_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Eventos
        self.remote_tree.bind('<Double-1>', self.on_remote_item_double_click)
    
    def create_status_panel(self, parent):
        """Crear panel de estado y progreso"""
        status_frame = ttk.LabelFrame(parent, text="📊 Estado del Sistema", style='TLabelframe')
        status_frame.pack(fill=tk.X, pady=(15, 0))
        
        # Barra de progreso mejorada
        progress_frame = ttk.Frame(status_frame, style='Panel.TFrame')
        progress_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # Etiqueta de progreso
        progress_info_frame = ttk.Frame(progress_frame, style='Panel.TFrame')
        progress_info_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(progress_info_frame, text="📈 Progreso:", 
                 background='#2d2d2d', foreground='#ffffff', 
                 font=('Segoe UI', 10, 'bold')).pack(side=tk.LEFT)
        
        self.progress_label = ttk.Label(progress_info_frame, text="Sistema listo", style='Status.TLabel')
        self.progress_label.pack(side=tk.LEFT, padx=10)
        
        # Barra de progreso
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, 
                                          variable=self.progress_var, 
                                          maximum=100, 
                                          length=500,
                                          style='TProgressbar')
        self.progress_bar.pack(pady=5)
        
        # Separador
        ttk.Separator(status_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        # Información de estado en columnas
        info_frame = ttk.Frame(status_frame, style='Panel.TFrame')
        info_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # Columna 1: Cola de descargas
        col1_frame = ttk.Frame(info_frame, style='Panel.TFrame')
        col1_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        ttk.Label(col1_frame, text="📋 Cola de Descargas:", 
                 background='#2d2d2d', foreground='#00ccff', 
                 font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W)
        self.queue_label = ttk.Label(col1_frame, text="Pendientes: 0 | Activas: 0", style='Status.TLabel')
        self.queue_label.pack(anchor=tk.W, pady=(2, 0))
        
        # Columna 2: Estadísticas de descarga
        col2_frame = ttk.Frame(info_frame, style='Panel.TFrame')
        col2_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20)
        
        ttk.Label(col2_frame, text="📊 Estadísticas:", 
                 background='#2d2d2d', foreground='#00ccff', 
                 font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W)
        self.download_label = ttk.Label(col2_frame, text="Completadas: 0 | Fallidas: 0", style='Status.TLabel')
        self.download_label.pack(anchor=tk.W, pady=(2, 0))
        
        # Columna 3: Velocidad
        col3_frame = ttk.Frame(info_frame, style='Panel.TFrame')
        col3_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        ttk.Label(col3_frame, text="🚀 Rendimiento:", 
                 background='#2d2d2d', foreground='#00ccff', 
                 font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W)
        self.speed_label = ttk.Label(col3_frame, text="Velocidad: 0 KB/s", style='Status.TLabel')
        self.speed_label.pack(anchor=tk.W, pady=(2, 0))
    
    def create_context_menu(self):
        """Crear menú contextual para el M3U tree"""
        self.context_menu = tk.Menu(self.root, tearoff=0, bg='#404040', fg='#ffffff')
        self.context_menu.add_command(label="⬇️ Descargar seleccionado", command=self.download_selected)
        self.context_menu.add_command(label="📁 Descargar serie completa", command=self.download_complete_series)
        self.context_menu.add_command(label="🎬 Descargar temporada", command=self.download_season)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 Copiar URL", command=self.copy_url)
        self.context_menu.add_command(label="ℹ️ Información", command=self.show_item_info)
    
    def setup_download_system(self):
        """Configurar el sistema de descargas"""
        self.download_thread = None
        self.download_stats = {
            'completed': 0,
            'failed': 0,
            'total': 0,
            'current_speed': 0
        }
        
        # Iniciar el monitor de cola
        self.monitor_queue()
    
    def monitor_queue(self):
        """Monitorear la cola de descargas - Legacy method"""
        # Este método se mantiene para compatibilidad pero redirige al nuevo sistema
        self.monitor_downloads()
    
    def load_m3u_file(self):
        """Cargar archivo M3U desde disco"""
        self.debug_log_button_click("Cargar Archivo M3U", "load_m3u_file")
        
        try:
            file_path = filedialog.askopenfilename(
                title="Seleccionar archivo M3U",
                filetypes=[("M3U files", "*.m3u"), ("M3U8 files", "*.m3u8"), ("All files", "*.*")]
            )
            
            if file_path:
                self.logger.info(f"Cargando archivo M3U: {file_path}")
                self.progress_label.config(text="Cargando archivo M3U...")
                
                # Cargar en thread separado con manejo seguro
                def load_task():
                    try:
                        self.logger.debug("Iniciando parseo de archivo M3U...")
                        items = self.m3u_parser.parse_file(file_path)
                        self.logger.info(f"Archivo M3U parseado exitosamente: {len(items)} items")
                        self.root.after(0, lambda: self.display_m3u_content(items))
                    except Exception as e:
                        self.logger.error(f"Error cargando M3U: {e}")
                        self.root.after(0, lambda: messagebox.showerror("Error", f"Error cargando M3U: {e}"))
                
                self.safe_thread_execution(load_task)
                
        except Exception as e:
            self.logger.error(f"Error seleccionando archivo: {e}")
            messagebox.showerror("Error", f"Error seleccionando archivo: {e}")
    
    def load_m3u_url(self):
        """Cargar M3U desde URL"""
        self.debug_log_button_click("Cargar URL M3U", "load_m3u_url")
        
        try:
            url = simpledialog.askstring("Cargar M3U", "Ingrese la URL del archivo M3U:")
            
            if url:
                self.logger.info(f"Cargando M3U desde URL: {url}")
                self.progress_label.config(text="Descargando M3U desde URL...")
                
                def load_task():
                    try:
                        self.logger.debug("Iniciando descarga y parseo de M3U desde URL...")
                        items = self.m3u_parser.parse_url(url)
                        self.logger.info(f"M3U desde URL parseado exitosamente: {len(items)} items")
                        self.root.after(0, lambda: self.display_m3u_content(items))
                    except Exception as e:
                        self.logger.error(f"Error cargando M3U desde URL: {e}")
                        self.root.after(0, lambda: messagebox.showerror("Error", f"Error cargando M3U: {e}"))
                
                self.safe_thread_execution(load_task)
                
        except Exception as e:
            self.logger.error(f"Error: {e}")
            messagebox.showerror("Error", f"Error: {e}")
    
    def display_m3u_content(self, items: List[M3UItem]):
        """Mostrar contenido M3U en el tree"""
        self.logger.debug(f"Mostrando {len(items)} items M3U en interfaz")
        
        try:
            # Limpiar tree
            for item in self.m3u_tree.get_children():
                self.m3u_tree.delete(item)
            
            # Organizar por grupos
            groups = {}
            for item in items:
                group = item.group or "Sin grupo"
                if group not in groups:
                    groups[group] = []
                groups[group].append(item)
            
            self.logger.debug(f"Items organizados en {len(groups)} grupos")
            
            # Insertar elementos
            for group_name, group_items in groups.items():
                # Crear nodo del grupo
                group_node = self.m3u_tree.insert("", "end", text=f"📁 {group_name}", 
                                                 values=("grupo", group_name, "", "", f"{len(group_items)} elementos"))
                
                # Insertar elementos del grupo
                for item in group_items:
                    icon = self.get_content_icon(item.content_type)
                    self.m3u_tree.insert(group_node, "end", text=icon,
                                        values=(item.content_type.value, item.name, item.group, item.url, "Listo"))
            
            # Expandir grupos
            for child in self.m3u_tree.get_children():
                self.m3u_tree.item(child, open=True)
            
            self.m3u_content = items
            self.progress_label.config(text=f"Cargados {len(items)} elementos")
            
            # Mostrar estadísticas
            stats = self.m3u_parser.get_statistics()
            self.logger.info(f"Estadísticas M3U: {stats}")
            stats_text = f"Total: {stats['total']} | Películas: {stats['movies']} | Series: {stats['series']} | Live: {stats['live']}"
            messagebox.showinfo("M3U Cargado", stats_text)
            
        except Exception as e:
            self.logger.error(f"Error mostrando contenido M3U: {e}")
            messagebox.showerror("Error", f"Error mostrando contenido: {e}")
    
    def get_content_icon(self, content_type: ContentType) -> str:
        """Obtener icono según tipo de contenido"""
        icons = {
            ContentType.MOVIE: "🎬",
            ContentType.SERIES: "📺",
            ContentType.LIVE: "📡",
            ContentType.UNKNOWN: "❓"
        }
        return icons.get(content_type, "❓")
    
    def connect_ssh(self):
        """Conectar via SSH/SFTP"""
        self.debug_log_button_click("Conectar SSH", "connect_ssh")
        
        try:
            # Obtener datos de conexión
            server = self.server_entry.get().strip()
            username = self.user_entry.get().strip()
            password = self.password_entry.get().strip()
            port = int(self.port_entry.get().strip() or "22")
            
            self.logger.info(f"Intentando conectar SSH: {username}@{server}:{port}")
            
            if not server or not username:
                self.logger.warning("Faltan datos de conexión SSH")
                messagebox.showerror("Error", "Debe ingresar servidor y usuario")
                return
            
            if not password:
                self.logger.debug("Solicitando contraseña SSH")
                # Si no hay contraseña en el campo, solicitarla
                password = simpledialog.askstring("SSH", "Ingrese la contraseña:", show='*')
                if not password:
                    self.logger.info("Conexión SSH cancelada por usuario")
                    return
            
            self.ssh_status.config(text="🔄 Conectando...", foreground='#ffaa00')
            self.progress_label.config(text="Conectando SSH...")
            
            def connect_task():
                try:
                    self.logger.debug("Iniciando conexión SSH...")
                    success = self.ssh_manager.connect(server, username, password, port)
                    
                    if success:
                        self.logger.info("Conexión SSH exitosa")
                        # Configurar download manager
                        self.download_manager.set_ssh_manager(self.ssh_manager)
                        
                        # Actualizar interfaz
                        self.root.after(0, self.on_ssh_connected)
                        
                        # Cargar directorio inicial
                        self.root.after(0, self.refresh_remote_browser)
                    else:
                        self.logger.error("Falló la conexión SSH")
                        self.root.after(0, lambda: self.ssh_status.config(text="❌ Error de conexión", foreground='#ff0000'))
                        self.root.after(0, lambda: messagebox.showerror("Error", "No se pudo conectar al servidor"))
                
                except Exception as e:
                    self.logger.error(f"Error conectando SSH: {e}")
                    self.root.after(0, lambda: self.ssh_status.config(text="❌ Error", foreground='#ff0000'))
                    self.root.after(0, lambda: messagebox.showerror("Error", f"Error conectando: {e}"))
            
            self.safe_thread_execution(connect_task)
            
        except Exception as e:
            self.logger.error(f"Error en conexión SSH: {e}")
            messagebox.showerror("Error", f"Error en conexión SSH: {e}")
    
    def disconnect_ssh(self):
        """Desconectar SSH/SFTP"""
        self.debug_log_button_click("Desconectar SSH", "disconnect_ssh")
        
        try:
            if self.ssh_manager.is_connected:
                self.logger.info("Desconectando SSH...")
                self.ssh_manager.disconnect()
                self.ssh_status.config(text="❌ Desconectado", foreground='#ff6666')
                self.progress_label.config(text="SSH desconectado")
                
                # Limpiar navegador remoto
                for item in self.remote_tree.get_children():
                    self.remote_tree.delete(item)
                
                # Limpiar carpeta destino
                self.destination_folder = ""
                self.dest_label.config(text="No seleccionada")
                
                self.logger.info("SSH desconectado exitosamente")
                messagebox.showinfo("SSH", "Desconectado exitosamente")
            else:
                self.logger.warning("Intento de desconectar SSH sin conexión activa")
                messagebox.showinfo("SSH", "No hay conexión activa")
                
        except Exception as e:
            self.logger.error(f"Error desconectando SSH: {e}")
            messagebox.showerror("Error", f"Error desconectando: {e}")
    
    def on_ssh_connected(self):
        """Callback cuando SSH se conecta"""
        self.ssh_status.config(text="✅ Conectado", foreground='#00ff00')
        self.progress_label.config(text="SSH conectado exitosamente")
        self.current_remote_path = self.ssh_manager.current_path
    
    def refresh_remote_browser(self):
        """Actualizar navegador remoto"""
        self.debug_log_button_click("Actualizar Navegador", "refresh_remote_browser")
        
        if not self.ssh_manager.is_connected:
            self.logger.warning("Intento de actualizar navegador sin conexión SSH")
            return
        
        try:
            self.logger.debug(f"Actualizando navegador remoto: {self.current_remote_path}")
            
            def refresh_task():
                try:
                    self.logger.debug("Listando directorio remoto...")
                    files = self.ssh_manager.list_directory(self.current_remote_path)
                    self.logger.debug(f"Encontrados {len(files)} archivos")
                    self.root.after(0, lambda: self.display_remote_files(files))
                except Exception as e:
                    self.logger.error(f"Error listando archivos: {e}")
                    self.root.after(0, lambda: messagebox.showerror("Error", f"Error listando archivos: {e}"))
            
            self.safe_thread_execution(refresh_task)
            
        except Exception as e:
            self.logger.error(f"Error actualizando navegador: {e}")
            messagebox.showerror("Error", f"Error actualizando navegador: {e}")
    
    def display_remote_files(self, files: List[RemoteFile]):
        """Mostrar archivos remotos"""
        try:
            # Limpiar tree
            for item in self.remote_tree.get_children():
                self.remote_tree.delete(item)
            
            # Agregar directorio padre si no estamos en raíz
            if self.current_remote_path != "/":
                parent_path = os.path.dirname(self.current_remote_path)
                self.remote_tree.insert("", "end", text="📁",
                                       values=("..", "<DIR>", "", "", parent_path))
            
            # Insertar archivos
            for file in files:
                icon = "📁" if file.file_type == FileType.DIRECTORY else "📄"
                size_str = self.format_file_size(file.size) if file.file_type == FileType.FILE else "<DIR>"
                date_str = file.modified.strftime("%Y-%m-%d %H:%M")
                
                self.remote_tree.insert("", "end", text=icon,
                                       values=(file.name, size_str, date_str, file.permissions, file.path))
            
        except Exception as e:
            logging.error(f"Error mostrando archivos remotos: {e}")
    
    def format_file_size(self, size: int) -> str:
        """Formatear tamaño de archivo"""
        size_float = float(size)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_float < 1024.0:
                return f"{size_float:.1f} {unit}"
            size_float /= 1024.0
        return f"{size_float:.1f} TB"
    
    def show_context_menu(self, event):
        """Mostrar menú contextual"""
        try:
            # Obtener elemento seleccionado
            selection = self.m3u_tree.selection()
            if selection:
                self.context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            logging.error(f"Error mostrando menú contextual: {e}")
    
    def on_item_double_click(self, event):
        """Manejar doble clic en item"""
        try:
            selection = self.m3u_tree.selection()
            if selection:
                item = self.m3u_tree.item(selection[0])
                values = item['values']
                
                if len(values) > 1:
                    name = values[1]
                    url = values[3] if len(values) > 3 else ""
                    
                    # Mostrar información del item
                    info = f"Nombre: {name}\nURL: {url}"
                    messagebox.showinfo("Información del Item", info)
                    
        except Exception as e:
            logging.error(f"Error en doble clic: {e}")
    
    def start_downloads(self):
        """Iniciar descargas"""
        self.debug_log_button_click("Iniciar Descargas", "start_downloads")
        
        try:
            if not self.ssh_manager.is_connected:
                self.logger.warning("Intento de iniciar descargas sin conexión SSH")
                messagebox.showerror("Error", "Debe conectarse primero via SSH")
                return
            
            if not self.destination_folder:
                self.logger.warning("Intento de iniciar descargas sin carpeta destino")
                messagebox.showerror("Error", "Debe seleccionar una carpeta destino")
                return
            
            queue_status = self.download_manager.get_queue_status()
            if queue_status['pending'] == 0:
                self.logger.info("No hay descargas en cola")
                messagebox.showinfo("Info", "No hay descargas en cola")
                return
            
            self.logger.info(f"Iniciando {queue_status['pending']} descargas")
            self.download_manager.start_downloads()
            self.progress_label.config(text="Iniciando descargas...")
            
            messagebox.showinfo("Descargas", "Descargas iniciadas")
            
        except Exception as e:
            self.logger.error(f"Error iniciando descargas: {e}")
            messagebox.showerror("Error", f"Error iniciando descargas: {e}")
    
    def stop_downloads(self):
        """Detener descargas"""
        self.debug_log_button_click("Detener Descargas", "stop_downloads")
        
        try:
            self.logger.info("Deteniendo descargas...")
            self.download_manager.stop_downloads()
            self.progress_label.config(text="Descargas detenidas")
            messagebox.showinfo("Descargas", "Descargas detenidas")
            
        except Exception as e:
            self.logger.error(f"Error deteniendo descargas: {e}")
            messagebox.showerror("Error", f"Error deteniendo descargas: {e}")
    
    def clear_queue(self):
        """Limpiar cola de descargas"""
        self.debug_log_button_click("Limpiar Cola", "clear_queue")
        
        try:
            queue_status = self.download_manager.get_queue_status()
            self.logger.info(f"Solicitando limpiar cola con {queue_status['pending']} elementos")
            
            result = messagebox.askyesno("Confirmar", "¿Desea limpiar la cola de descargas?")
            if result:
                self.logger.info("Usuario confirmó limpiar cola")
                self.download_manager.clear_queue()
                self.progress_label.config(text="Cola limpiada")
                messagebox.showinfo("Cola", "Cola de descargas limpiada")
            else:
                self.logger.info("Usuario canceló limpiar cola")
                
        except Exception as e:
            self.logger.error(f"Error limpiando cola: {e}")
            messagebox.showerror("Error", f"Error limpiando cola: {e}")
    
    def create_remote_folder(self):
        """Crear carpeta remota"""
        self.debug_log_button_click("Crear Carpeta Remota", "create_remote_folder")
        
        try:
            if not self.ssh_manager.is_connected:
                self.logger.warning("Intento de crear carpeta sin conexión SSH")
                messagebox.showerror("Error", "No hay conexión SSH")
                return
            
            folder_name = simpledialog.askstring("Nueva Carpeta", "Nombre de la carpeta:")
            if folder_name:
                folder_path = os.path.join(self.current_remote_path, folder_name).replace('\\', '/')
                self.logger.info(f"Creando carpeta remota: {folder_path}")
                
                def create_task():
                    try:
                        self.logger.debug("Ejecutando creación de carpeta...")
                        success = self.ssh_manager.create_directory(folder_path)
                        if success:
                            self.logger.info(f"Carpeta creada exitosamente: {folder_name}")
                            self.root.after(0, self.refresh_remote_browser)
                            self.root.after(0, lambda: messagebox.showinfo("Éxito", f"Carpeta '{folder_name}' creada"))
                        else:
                            self.logger.error(f"No se pudo crear carpeta: {folder_name}")
                            self.root.after(0, lambda: messagebox.showerror("Error", "No se pudo crear la carpeta"))
                    except Exception as e:
                        self.logger.error(f"Error creando carpeta: {e}")
                        self.root.after(0, lambda: messagebox.showerror("Error", f"Error creando carpeta: {e}"))
                
                self.safe_thread_execution(create_task)
                
        except Exception as e:
            self.logger.error(f"Error: {e}")
            messagebox.showerror("Error", f"Error: {e}")
    
    def delete_remote_item(self):
        """Eliminar item remoto"""
        self.debug_log_button_click("Eliminar Item Remoto", "delete_remote_item")
        
        try:
            if not self.ssh_manager.is_connected:
                self.logger.warning("Intento de eliminar item sin conexión SSH")
                messagebox.showerror("Error", "No hay conexión SSH")
                return
            
            selection = self.remote_tree.selection()
            if not selection:
                self.logger.warning("Intento de eliminar item sin selección")
                messagebox.showerror("Error", "Debe seleccionar un elemento")
                return
            
            item = self.remote_tree.item(selection[0])
            values = item['values']
            
            if len(values) >= 5:
                name = values[0]
                path = values[4]
                is_dir = values[1] == "<DIR>"
                
                self.logger.info(f"Solicitando eliminar: {name} ({'directorio' if is_dir else 'archivo'})")
                
                result = messagebox.askyesno("Confirmar", f"¿Desea eliminar '{name}'?")
                if result:
                    self.logger.info(f"Usuario confirmó eliminar: {name}")
                    
                    def delete_task():
                        try:
                            self.logger.debug(f"Ejecutando eliminación de: {path}")
                            if is_dir:
                                success = self.ssh_manager.delete_directory(path)
                            else:
                                success = self.ssh_manager.delete_file(path)
                            
                            if success:
                                self.logger.info(f"Eliminado exitosamente: {name}")
                                self.root.after(0, self.refresh_remote_browser)
                                self.root.after(0, lambda: messagebox.showinfo("Éxito", f"'{name}' eliminado"))
                            else:
                                self.logger.error(f"No se pudo eliminar: {name}")
                                self.root.after(0, lambda: messagebox.showerror("Error", "No se pudo eliminar"))
                        except Exception as e:
                            self.logger.error(f"Error eliminando {name}: {e}")
                            self.root.after(0, lambda: messagebox.showerror("Error", f"Error eliminando: {e}"))
                    
                    self.safe_thread_execution(delete_task)
                else:
                    self.logger.info(f"Usuario canceló eliminar: {name}")
                    
        except Exception as e:
            self.logger.error(f"Error: {e}")
            messagebox.showerror("Error", f"Error: {e}")
    
    def on_remote_item_double_click(self, event):
        """Manejar doble clic en item remoto"""
        self.logger.debug("Doble clic en item remoto")
        
        try:
            selection = self.remote_tree.selection()
            if selection:
                item = self.remote_tree.item(selection[0])
                values = item['values']
                
                if len(values) >= 5:
                    name = values[0]
                    path = values[4]
                    is_dir = values[1] == "<DIR>"
                    
                    self.logger.debug(f"Doble clic en: {name} ({'directorio' if is_dir else 'archivo'})")
                    
                    if is_dir:
                        # Navegar al directorio
                        if name == "..":
                            self.current_remote_path = os.path.dirname(self.current_remote_path)
                            self.logger.debug(f"Navegando al directorio padre: {self.current_remote_path}")
                        else:
                            self.current_remote_path = path
                            self.logger.debug(f"Navegando al directorio: {self.current_remote_path}")
                        
                        self.refresh_remote_browser()
                        
        except Exception as e:
            self.logger.error(f"Error en doble clic remoto: {e}")
    
    def set_destination_folder(self):
        """Fijar carpeta destino"""
        self.debug_log_button_click("Fijar Carpeta Destino", "set_destination_folder")
        
        try:
            if not self.ssh_manager.is_connected:
                self.logger.warning("Intento de fijar carpeta destino sin conexión SSH")
                messagebox.showerror("Error", "No hay conexión SSH")
                return
            
            old_destination = self.destination_folder
            self.destination_folder = self.current_remote_path
            self.dest_label.config(text=self.destination_folder)
            
            self.logger.info(f"Carpeta destino cambiada de '{old_destination}' a '{self.destination_folder}'")
            messagebox.showinfo("Destino", f"Carpeta destino: {self.destination_folder}")
            
        except Exception as e:
            self.logger.error(f"Error fijando carpeta destino: {e}")
            messagebox.showerror("Error", f"Error: {e}")
    
    def download_selected(self):
        """Descargar elementos seleccionados"""
        self.debug_log_button_click("Descargar Seleccionados", "download_selected")
        
        try:
            if not self.destination_folder:
                self.logger.warning("Intento de descargar sin carpeta destino")
                messagebox.showerror("Error", "Debe seleccionar una carpeta destino")
                return
            
            selections = self.m3u_tree.selection()
            if not selections:
                self.logger.warning("Intento de descargar sin elementos seleccionados")
                messagebox.showerror("Error", "Debe seleccionar elementos para descargar")
                return
            
            self.logger.info(f"Procesando {len(selections)} elementos seleccionados para descarga")
            
            downloads = []
            for selection in selections:
                item = self.m3u_tree.item(selection)
                values = item['values']
                
                if len(values) >= 4:
                    content_type = values[0]
                    name = values[1]
                    url = values[3]
                    
                    if url and not url.startswith("http"):
                        self.logger.debug(f"Saltando URL inválida: {url}")
                        continue
                    
                    # Generar nombre de archivo
                    filename = self.generate_filename(name, url)
                    
                    downloads.append({
                        'url': url,
                        'filename': filename,
                        'remote_path': self.destination_folder,
                        'content_type': content_type
                    })
                    
                    self.logger.debug(f"Agregado para descarga: {name} -> {filename}")
            
            if downloads:
                self.logger.info(f"Agregando {len(downloads)} descargas a la cola")
                task_ids = self.download_manager.add_bulk_downloads(downloads)
                messagebox.showinfo("Descargas", f"Agregadas {len(downloads)} descargas a la cola")
            else:
                self.logger.warning("No se encontraron URLs válidas para descargar")
                messagebox.showerror("Error", "No se encontraron URLs válidas")
                
        except Exception as e:
            self.logger.error(f"Error agregando descargas: {e}")
            messagebox.showerror("Error", f"Error agregando descargas: {e}")
    
    def download_complete_series(self):
        """Descargar serie completa"""
        try:
            if not self.destination_folder:
                messagebox.showerror("Error", "Debe seleccionar una carpeta destino")
                return
            
            selection = self.m3u_tree.selection()
            if not selection:
                messagebox.showerror("Error", "Debe seleccionar una serie")
                return
            
            item = self.m3u_tree.item(selection[0])
            values = item['values']
            
            if len(values) >= 2:
                series_name = values[1]
                
                # Buscar todos los episodios de la serie
                episodes = self.m3u_parser.get_series_episodes(series_name)
                
                if episodes:
                    # Convertir a formato para descarga
                    downloads = []
                    for episode in episodes:
                        filename = self.generate_filename(episode.name, episode.url)
                        downloads.append({
                            'url': episode.url,
                            'filename': filename,
                            'remote_path': self.destination_folder,
                            'content_type': 'series',
                            'metadata': {
                                'series_name': series_name,
                                'episode_name': episode.name
                            }
                        })
                    
                    if downloads:
                        task_ids = self.download_manager.add_bulk_downloads(downloads)
                        messagebox.showinfo("Serie", f"Agregados {len(downloads)} episodios a la cola")
                    else:
                        messagebox.showerror("Error", "No se encontraron episodios válidos")
                else:
                    messagebox.showerror("Error", "No se encontraron episodios para esta serie")
                    
        except Exception as e:
            messagebox.showerror("Error", f"Error descargando serie: {e}")
    
    def download_season(self):
        """Descargar temporada"""
        try:
            season = simpledialog.askstring("Temporada", "Número de temporada:")
            if season:
                # Implementar lógica de temporada
                messagebox.showinfo("Temporada", f"Funcionalidad de temporada {season} en desarrollo")
                
        except Exception as e:
            messagebox.showerror("Error", f"Error: {e}")
    
    def copy_url(self):
        """Copiar URL al portapapeles"""
        try:
            selection = self.m3u_tree.selection()
            if selection:
                item = self.m3u_tree.item(selection[0])
                values = item['values']
                
                if len(values) >= 4:
                    url = values[3]
                    self.root.clipboard_clear()
                    self.root.clipboard_append(url)
                    messagebox.showinfo("Copiado", "URL copiada al portapapeles")
                    
        except Exception as e:
            messagebox.showerror("Error", f"Error copiando URL: {e}")
    
    def show_item_info(self):
        """Mostrar información del item"""
        try:
            selection = self.m3u_tree.selection()
            if selection:
                item = self.m3u_tree.item(selection[0])
                values = item['values']
                
                if len(values) >= 4:
                    info = f"""
Tipo: {values[0]}
Nombre: {values[1]}
Grupo: {values[2]}
URL: {values[3]}
Estado: {values[4]}
                    """
                    messagebox.showinfo("Información", info.strip())
                    
        except Exception as e:
            messagebox.showerror("Error", f"Error mostrando información: {e}")
    
    def generate_filename(self, name: str, url: str) -> str:
        """Generar nombre de archivo"""
        try:
            self.logger.debug(f"Generando nombre de archivo para: {name}")
            
            # Limpiar nombre
            import re
            clean_name = re.sub(r'[<>:"/\\|?*]', '_', name)
            clean_name = clean_name.strip()
            
            # Agregar extensión si no la tiene
            if not os.path.splitext(clean_name)[1]:
                clean_name += ".mp4"
            
            self.logger.debug(f"Nombre de archivo generado: {clean_name}")
            return clean_name
            
        except Exception as e:
            self.logger.error(f"Error generando nombre: {e}")
            fallback_name = f"download_{int(time.time())}.mp4"
            self.logger.debug(f"Usando nombre de respaldo: {fallback_name}")
            return fallback_name
    
    def add_thread(self, thread):
        """Agregar thread a la lista de threads activos"""
        with self.thread_lock:
            self.active_threads.append(thread)
            self.logger.debug(f"Thread agregado. Total activos: {len(self.active_threads)}")
    
    def remove_thread(self, thread):
        """Remover thread de la lista de threads activos"""
        with self.thread_lock:
            if thread in self.active_threads:
                self.active_threads.remove(thread)
                self.logger.debug(f"Thread removido. Total activos: {len(self.active_threads)}")
    
    def cleanup_threads(self):
        """Limpiar threads inactivos"""
        with self.thread_lock:
            self.active_threads = [t for t in self.active_threads if t.is_alive()]
            self.logger.debug(f"Threads limpiados. Total activos: {len(self.active_threads)}")
    
    def on_closing(self):
        """Manejar cierre de aplicación"""
        self.logger.info("=== CERRANDO APLICACIÓN ===")
        
        try:
            # Detener descargas
            self.logger.debug("Deteniendo descargas...")
            self.download_manager.stop_downloads()
            
            # Desconectar SSH
            if self.ssh_manager.is_connected:
                self.logger.debug("Desconectando SSH...")
                self.ssh_manager.disconnect()
            
            # Esperar threads activos
            self.logger.debug(f"Esperando {len(self.active_threads)} threads activos...")
            for thread in self.active_threads[:]:
                if thread.is_alive():
                    thread.join(timeout=2.0)
                    if thread.is_alive():
                        self.logger.warning(f"Thread {thread.name} no terminó a tiempo")
            
            self.logger.info("Aplicación cerrada correctamente")
            
        except Exception as e:
            self.logger.error(f"Error cerrando aplicación: {e}")
        finally:
            self.root.destroy()
    
    def safe_thread_execution(self, target_func, *args, **kwargs):
        """Ejecutar función en thread con manejo seguro"""
        def wrapper():
            thread = threading.current_thread()
            self.logger.debug(f"Iniciando thread: {thread.name}")
            
            try:
                result = target_func(*args, **kwargs)
                self.logger.debug(f"Thread completado: {thread.name}")
                return result
            except Exception as e:
                self.logger.error(f"Error en thread {thread.name}: {e}")
                # Mostrar error en UI de manera segura
                self.root.after(0, lambda: messagebox.showerror("Error", f"Error en operación: {e}"))
            finally:
                self.remove_thread(thread)
        
        thread = threading.Thread(target=wrapper, daemon=True)
        thread.name = f"Worker-{len(self.active_threads)}"
        self.add_thread(thread)
        thread.start()
        return thread
    
    def debug_log_button_click(self, button_name, operation):
        """Log debug para clicks de botones"""
        self.logger.debug(f"BUTTON CLICK: {button_name} - Operación: {operation}")
        
        # Verificar estado del sistema
        ssh_status = "Conectado" if self.ssh_manager.is_connected else "Desconectado"
        m3u_items = len(self.m3u_content)
        queue_status = self.download_manager.get_queue_status()
        
        self.logger.debug(f"Estado SSH: {ssh_status}")
        self.logger.debug(f"Items M3U: {m3u_items}")
        self.logger.debug(f"Cola descargas: {queue_status}")
        self.logger.debug(f"Carpeta destino: {self.destination_folder}")
        self.logger.debug(f"Threads activos: {len(self.active_threads)}")

def main():
    """Función principal"""
    try:
        # Configurar logging global
        global logger
        logger.info("=== INICIANDO APLICACIÓN M3U PARSER PRO ===")
        
        root = tk.Tk()
        app = M3UParserApp(root)
        
        logger.info("Iniciando bucle principal de la aplicación")
        root.mainloop()
        
        logger.info("=== APLICACIÓN TERMINADA ===")
        
    except Exception as e:
        logger.error(f"Error crítico: {e}")
        try:
            messagebox.showerror("Error Crítico", f"Error al iniciar la aplicación: {e}")
        except:
            print(f"Error crítico: {e}")

if __name__ == "__main__":
    main()
