# M3U Parser Pro - Descarga Remota

## 🎯 Descripción

**M3U Parser Pro** es una aplicación robusta y elegante para la gestión y descarga remota de contenido multimedia desde listas M3U/M3U8. Combina la precisión de un reloj suizo con el poder de un león para ofrecerte una experiencia de descarga excepcional.

## ✨ Características Principales

### 📺 **Módulo M3U Parser**
- ✅ Carga de listas M3U desde archivo local o URL
- ✅ Soporte completo para protocolo Xtream Codes
- ✅ Identificación automática de contenido (Películas, Series, TV en vivo)
- ✅ Organización por grupos y categorías
- ✅ Visualización estructurada con iconos

### 🖥️ **Módulo SSH/SFTP**
- ✅ Conexión segura a servidores remotos Ubuntu/Linux
- ✅ Navegación completa del sistema de archivos
- ✅ Creación y eliminación de carpetas
- ✅ Selección de carpeta destino persistente
- ✅ Interfaz estilo MobaXterm

### ⬇️ **Sistema de Descargas**
- ✅ Descarga remota directa (sin caché local)
- ✅ Uso de `wget` optimizado en el servidor
- ✅ Descargas concurrentes con control de hilos
- ✅ Renombrado automático y extensiones correctas
- ✅ Cola de descargas persistente

### 🎬 **Funciones Especializadas**
- ✅ Descarga de series completas
- ✅ Descarga por temporadas
- ✅ Descarga de episodios específicos
- ✅ Descarga masiva de películas
- ✅ Gestión inteligente de nombres de archivo

### 📊 **Monitoreo y Control**
- ✅ Barra de progreso animada
- ✅ Estadísticas en tiempo real
- ✅ Control de inicio/parada de descargas
- ✅ Gestión de errores y reintentos
- ✅ Logs detallados

## 🚀 Instalación

### Requisitos Previos
- **Python 3.7+** instalado
- **Conexión a Internet** para instalar dependencias
- **Servidor SSH/SFTP** remoto (Ubuntu/Linux recomendado)

### Instalación Automática (Windows)
1. Ejecutar `install.bat` como administrador
2. Seguir las instrucciones en pantalla

### Instalación Manual
```bash
# Clonar o descargar el proyecto
cd M3U_Parser_Pro

# Instalar dependencias
pip install -r requirements.txt

# Ejecutar aplicación
python main.py
```

## 🎮 Uso de la Aplicación

### 1. **Cargar Contenido M3U**
- **Archivo Local**: Botón "📁 Cargar M3U" → Seleccionar archivo `.m3u` o `.m3u8`
- **Desde URL**: Botón "🔗 Cargar desde URL" → Ingresar URL del M3U

### 2. **Conectar al Servidor**
- **Datos de Conexión**: 
  - Servidor: IP o hostname del servidor
  - Usuario: nombre de usuario SSH
  - Puerto: 22 (por defecto)
- **Botón "🖥️ Conectar SSH"** → Ingresar contraseña

### 3. **Navegar y Configurar**
- **Explorar carpetas** en el panel derecho
- **Crear carpetas** con el botón "📁 Nueva carpeta"
- **Seleccionar destino** con "📌 Fijar como destino"

### 4. **Descargar Contenido**
- **Seleccionar elementos** en el panel M3U
- **Clic derecho** para opciones:
  - ⬇️ Descargar seleccionado
  - 📁 Descargar serie completa
  - 🎬 Descargar temporada
- **Iniciar descargas** con "⬇️ Iniciar Descargas"

## 🔧 Configuración Avanzada

### SSH/SFTP
```python
# Configuración de conexión
servidor = "*************"
usuario = "username"
puerto = 22
```

### Descargas
```python
# Configuración de wget
max_reintentos = 3
timeout = 30
descargas_concurrentes = 3
```

## 📝 Ejemplos de Uso

### Descarga de Serie Completa
1. Cargar M3U con series
2. Seleccionar serie en el tree
3. Clic derecho → "📁 Descargar serie completa"
4. Automáticamente descarga todos los episodios

### Descarga Masiva de Películas
1. Filtrar por grupo "Películas"
2. Seleccionar múltiples películas (Ctrl+Click)
3. Clic derecho → "⬇️ Descargar seleccionado"
4. Todas las películas se agregan a la cola

### Organización por Carpetas
1. Navegar al directorio deseado
2. Crear carpeta por género: "Acción", "Comedia", etc.
3. Fijar como destino la carpeta específica
4. Descargar contenido categorizado

## 🛠️ Estructura del Proyecto

```
M3U_Parser_Pro/
├── main.py                 # Aplicación principal
├── m3u_parser.py          # Parser M3U y Xtream Codes
├── ssh_manager.py         # Gestión SSH/SFTP
├── download_manager.py    # Gestor de descargas
├── requirements.txt       # Dependencias
├── install.bat           # Instalador Windows
├── run.bat              # Ejecutor Windows
├── README.md            # Documentación
└── m3u_parser.log       # Logs de la aplicación
```

## 🐛 Solución de Problemas

### Error de Conexión SSH
```
❌ Error: No se puede conectar
```
**Solución**: Verificar IP, puerto, credenciales y firewall del servidor

### Error de Dependencias
```
❌ ModuleNotFoundError: No module named 'paramiko'
```
**Solución**: Ejecutar `pip install -r requirements.txt`

### Descargas Lentas
```
⚠️ Velocidad baja de descarga
```
**Solución**: Verificar conexión del servidor, no del cliente local

### Archivos No Encontrados
```
❌ Error 404 en descarga
```
**Solución**: Verificar URLs del M3U, algunas pueden estar caducadas

## 📊 Rendimiento

- **Descargas Concurrentes**: Hasta 3 simultáneas
- **Velocidad**: Limitada por el servidor remoto
- **Memoria**: Bajo uso (solo metadatos, no archivos)
- **CPU**: Mínimo uso (delegado al servidor)

## 🔒 Seguridad

- **Conexiones SSH**: Encriptadas con paramiko
- **Contraseñas**: No se almacenan en disco
- **Logs**: Sin información sensible
- **Archivos**: Descarga directa en servidor

## 📈 Estadísticas

La aplicación proporciona:
- 📊 Total de descargas
- ✅ Descargas completadas
- ❌ Descargas fallidas
- 📊 Velocidad promedio
- 📊 Tiempo total de descarga

## 🎯 Casos de Uso Ideales

1. **Administradores de Servidores Media**
   - Descargas masivas organizadas
   - Gestión remota de contenido

2. **Usuarios Avanzados**
   - Automatización de descargas
   - Organización por categorías

3. **Centros de Datos**
   - Descarga directa sin transferencia local
   - Optimización de ancho de banda

## 📞 Soporte

Para problemas o sugerencias:
- Revisar logs en `m3u_parser.log`
- Verificar conexión SSH manualmente
- Comprobar URLs de M3U en navegador

## 🔄 Actualizaciones Futuras

- [ ] Soporte para más protocolos de descarga
- [ ] Programación de descargas
- [ ] Integración con bases de datos
- [ ] Interfaz web opcional
- [ ] Soporte para múltiples servidores

---

**M3U Parser Pro** - La herramienta definitiva para descargas remotas de contenido multimedia.
*Preciso como un reloj suizo, poderoso como un león.* 🦁⚡
