@echo off
echo ===============================================
echo    M3U Parser Pro - Instalador de Dependencias
echo ===============================================
echo.

echo Verificando Python...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python no está instalado o no está en PATH
    echo Descargue Python desde https://python.org
    pause
    exit /b 1
)

echo.
echo Instalando dependencias...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ERROR: No se pudieron instalar las dependencias
    pause
    exit /b 1
)

echo.
echo ===============================================
echo    Instalación completada exitosamente
echo ===============================================
echo.
echo Para ejecutar la aplicación, use:
echo    python main.py
echo.
pause
