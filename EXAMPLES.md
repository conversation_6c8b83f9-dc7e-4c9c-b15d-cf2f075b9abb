# M3U Parser Pro - Ejemplos de Uso

## 📋 Ejemplos Prácticos

### 1. Configuración Inicial Básica

#### Conexión SSH
```
Servidor: 192.168.1.100
Usuario: mediaserver
Puerto: 22
Contraseña: [tu_contrase<PERSON>]
```

#### Estructura de Carpetas Recomendada
```
/home/<USER>/
├── downloads/
│   ├── movies/
│   │   ├── action/
│   │   ├── comedy/
│   │   └── drama/
│   └── series/
│       ├── complete/
│       └── ongoing/
└── temp/
```

### 2. Descarga de Películas por Género

#### Ejemplo: Películas de Acción
1. **Cargar M3U**: Archivo con películas categorizadas
2. **Filtrar por grupo**: Seleccionar grupo "Action Movies"
3. **Crear destino**: `/home/<USER>/downloads/movies/action/`
4. **Seleccionar películas**: Ctrl+Click para selección múltiple
5. **Descargar**: Clic derecho → "Descargar seleccionado"

#### Resultado Esperado
```
/home/<USER>/downloads/movies/action/
├── The.Matrix.1999.mp4
├── John.Wick.2014.mp4
├── Mad.Max.Fury.Road.2015.mp4
└── Terminator.2.1991.mp4
```

### 3. Descarga de Series Completas

#### Ejemplo: Serie "Breaking Bad"
1. **Localizar serie**: Buscar en el árbol M3U
2. **Seleccionar serie**: Clic en el nodo principal
3. **Crear carpeta**: `/home/<USER>/downloads/series/complete/Breaking.Bad/`
4. **Descargar completa**: Clic derecho → "Descargar serie completa"

#### Resultado Esperado
```
/home/<USER>/downloads/series/complete/Breaking.Bad/
├── Breaking.Bad.S01E01.Pilot.mp4
├── Breaking.Bad.S01E02.Cat.in.the.Bag.mp4
├── Breaking.Bad.S01E03.And.the.Bags.in.the.River.mp4
├── ...
├── Breaking.Bad.S05E15.Granite.State.mp4
└── Breaking.Bad.S05E16.Felina.mp4
```

### 4. Descarga por Temporadas

#### Ejemplo: Solo Temporada 1 de "Game of Thrones"
1. **Seleccionar episodios**: Filtrar por "S01E"
2. **Crear carpeta**: `/home/<USER>/downloads/series/seasons/Game.of.Thrones.S01/`
3. **Descargar temporada**: Seleccionar todos los episodios S01

#### Resultado Esperado
```
/home/<USER>/downloads/series/seasons/Game.of.Thrones.S01/
├── Game.of.Thrones.S01E01.Winter.Is.Coming.mp4
├── Game.of.Thrones.S01E02.The.Kingsroad.mp4
├── Game.of.Thrones.S01E03.Lord.Snow.mp4
├── ...
└── Game.of.Thrones.S01E10.Fire.and.Blood.mp4
```

### 5. Organización Automática por Género

#### Estructura M3U de Ejemplo
```
#EXTM3U
#EXTINF:-1 group-title="Action Movies",The Matrix
http://server.com/movies/matrix.mp4
#EXTINF:-1 group-title="Comedy Movies",The Hangover
http://server.com/movies/hangover.mp4
#EXTINF:-1 group-title="Drama Series",Breaking Bad S01E01
http://server.com/series/breaking-bad-s01e01.mp4
```

#### Flujo de Trabajo
1. **Cargar M3U**: La aplicación detecta automáticamente los grupos
2. **Crear carpetas por género**: 
   - Action Movies → `/downloads/movies/action/`
   - Comedy Movies → `/downloads/movies/comedy/`
   - Drama Series → `/downloads/series/drama/`
3. **Descargar por grupo**: Seleccionar grupo completo y descargar

### 6. Monitoreo de Descargas Masivas

#### Ejemplo: 100 Películas
1. **Preparar lista**: Seleccionar 100 películas
2. **Configurar destino**: `/home/<USER>/downloads/movies/bulk/`
3. **Iniciar descarga masiva**: Todas se agregan a la cola
4. **Monitorear progreso**: 
   - Cola: 97 pendientes
   - Activas: 3 descargando
   - Completadas: 0
   - Velocidad: 5.2 MB/s

#### Monitoreo en Tiempo Real
```
🔄 Progreso: 15/100 (15%)
⏱️ Tiempo transcurrido: 45 minutos
📊 Velocidad promedio: 4.8 MB/s
⏳ Tiempo estimado restante: 3.2 horas
```

### 7. Gestión de Errores y Reintentos

#### Escenario: Algunos enlaces no funcionan
1. **Descargas fallidas**: Se muestran en rojo
2. **Reintentos automáticos**: 3 intentos por archivo
3. **Revisión manual**: Verificar URLs problemáticas
4. **Reintento selectivo**: Volver a intentar solo los fallidos

#### Log de Errores Típico
```
2024-07-07 15:30:15 - ERROR - Descarga falló: Movie.Name.2024.mp4 - 404 Not Found
2024-07-07 15:30:16 - INFO - Reintentando descarga: Movie.Name.2024.mp4 (intento 2/3)
2024-07-07 15:30:45 - ERROR - Descarga falló: Movie.Name.2024.mp4 - Connection timeout
2024-07-07 15:30:46 - INFO - Reintentando descarga: Movie.Name.2024.mp4 (intento 3/3)
2024-07-07 15:31:15 - ERROR - Descarga falló definitivamente: Movie.Name.2024.mp4
```

### 8. Configuración para Diferentes Velocidades

#### Conexión Rápida (100+ Mbps)
```
- Descargas concurrentes: 5
- Timeout: 30 segundos
- Reintentos: 3
```

#### Conexión Estándar (10-50 Mbps)
```
- Descargas concurrentes: 3
- Timeout: 60 segundos
- Reintentos: 3
```

#### Conexión Lenta (< 10 Mbps)
```
- Descargas concurrentes: 1
- Timeout: 120 segundos
- Reintentos: 5
```

### 9. Casos de Uso Avanzados

#### Servidor de Medios Automatizado
1. **Horario programado**: Descargas nocturnas
2. **Organización automática**: Por género y año
3. **Limpieza automática**: Eliminar archivos temporales
4. **Backup automático**: Respaldo de listas M3U

#### Centro de Datos
1. **Múltiples servidores**: Balanceo de carga
2. **Almacenamiento distribuido**: RAID y redundancia
3. **Monitoreo 24/7**: Alertas automáticas
4. **Escalabilidad**: Hasta 1000+ descargas/día

### 10. Mejores Prácticas

#### Nomenclatura de Archivos
```
Películas: Movie.Name.Year.Quality.mp4
Series: Series.Name.SxxExx.Episode.Name.mp4
Documentales: Documentary.Name.Year.mp4
```

#### Estructura de Carpetas
```
/media/
├── movies/
│   ├── 2024/
│   ├── 2023/
│   └── classics/
├── series/
│   ├── ongoing/
│   └── completed/
└── documentaries/
    ├── nature/
    └── history/
```

#### Mantenimiento Regular
1. **Limpieza semanal**: Eliminar descargas incompletas
2. **Verificación mensual**: Comprobar integridad de archivos
3. **Actualización trimestral**: Listas M3U frescas
4. **Backup semestral**: Respaldo de configuraciones

### 11. Solución de Problemas Comunes

#### Problema: "SSH no conecta"
```
Solución:
1. Verificar IP y puerto
2. Comprobar credenciales
3. Revisar firewall
4. Probar conexión manual: ssh usuario@servidor
```

#### Problema: "Descargas muy lentas"
```
Solución:
1. Reducir descargas concurrentes
2. Verificar ancho de banda del servidor
3. Probar en horario diferente
4. Verificar carga del servidor
```

#### Problema: "Muchas descargas fallan"
```
Solución:
1. Verificar validez de URLs
2. Actualizar lista M3U
3. Aumentar timeout
4. Revisar logs del servidor
```

### 12. Automatización con Scripts

#### Script de Limpieza (bash)
```bash
#!/bin/bash
# Limpiar archivos temporales
find /home/<USER>/downloads/ -name "*.tmp" -delete
find /home/<USER>/downloads/ -name "*.part" -delete
echo "Limpieza completada: $(date)"
```

#### Script de Monitoreo (python)
```python
#!/usr/bin/env python3
import os
import time

def monitor_downloads():
    download_dir = "/home/<USER>/downloads"
    while True:
        size = sum(os.path.getsize(os.path.join(dirpath, filename))
                  for dirpath, dirnames, filenames in os.walk(download_dir)
                  for filename in filenames)
        print(f"Total descargado: {size / (1024**3):.2f} GB")
        time.sleep(300)  # Cada 5 minutos

if __name__ == "__main__":
    monitor_downloads()
```

---

## 🎯 Conclusión

Con estos ejemplos, tienes una guía completa para aprovechar al máximo **M3U Parser Pro**. La aplicación está diseñada para ser flexible y potente, adaptándose a diferentes necesidades y escenarios de uso.

¡Disfruta de tu experiencia de descarga eficiente y organizada! 🚀
