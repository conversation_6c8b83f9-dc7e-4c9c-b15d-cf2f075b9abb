import re
import requests
import json
import urllib.parse
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum

class ContentType(Enum):
    MOVIE = "movie"
    SERIES = "series"
    LIVE = "live"
    UNKNOWN = "unknown"

@dataclass
class M3UItem:
    """Representa un elemento M3U"""
    name: str
    url: str
    group: str = ""
    logo: str = ""
    content_type: ContentType = ContentType.UNKNOWN
    duration: int = -1
    tvg_id: str = ""
    tvg_name: str = ""
    extra_info: Optional[Dict] = None
    
    def __post_init__(self):
        if self.extra_info is None:
            self.extra_info = {}

class XtreamCodesAPI:
    """Manejo de API Xtream Codes"""
    
    def __init__(self, base_url: str, username: str, password: str):
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.auth_params = {
            'username': username,
            'password': password
        }
        
    def test_connection(self) -> bool:
        """Probar conexión con el servidor"""
        try:
            response = self.session.get(f"{self.base_url}/player_api.php", 
                                      params=self.auth_params, timeout=10)
            return response.status_code == 200
        except Exception as e:
            logging.error(f"Error probando conexión Xtream: {e}")
            return False
    
    def get_categories(self, content_type: str = "vod") -> List[Dict]:
        """Obtener categorías disponibles"""
        try:
            params = self.auth_params.copy()
            params['action'] = f'get_{content_type}_categories'
            
            response = self.session.get(f"{self.base_url}/player_api.php", 
                                      params=params, timeout=15)
            
            if response.status_code == 200:
                return response.json()
            return []
        except Exception as e:
            logging.error(f"Error obteniendo categorías: {e}")
            return []
    
    def get_streams(self, category_id: Optional[str] = None, content_type: str = "vod") -> List[Dict]:
        """Obtener streams por categoría"""
        try:
            params = self.auth_params.copy()
            params['action'] = f'get_{content_type}_streams'
            
            if category_id:
                params['category_id'] = category_id
            
            response = self.session.get(f"{self.base_url}/player_api.php", 
                                      params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            return []
        except Exception as e:
            logging.error(f"Error obteniendo streams: {e}")
            return []
    
    def get_series_info(self, series_id: str) -> Dict:
        """Obtener información detallada de una serie"""
        try:
            params = self.auth_params.copy()
            params['action'] = 'get_series_info'
            params['series_id'] = series_id
            
            response = self.session.get(f"{self.base_url}/player_api.php", 
                                      params=params, timeout=20)
            
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            logging.error(f"Error obteniendo info de serie: {e}")
            return {}
    
    def get_vod_info(self, vod_id: str) -> Dict:
        """Obtener información detallada de VOD"""
        try:
            params = self.auth_params.copy()
            params['action'] = 'get_vod_info'
            params['vod_id'] = vod_id
            
            response = self.session.get(f"{self.base_url}/player_api.php", 
                                      params=params, timeout=20)
            
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            logging.error(f"Error obteniendo info de VOD: {e}")
            return {}

class M3UParser:
    """Parser avanzado para archivos M3U"""
    
    def __init__(self):
        self.items = []
        self.xtream_api = None
        
    def parse_file(self, file_path: str) -> List[M3UItem]:
        """Parsear archivo M3U desde disco"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return self.parse_content(content)
        except Exception as e:
            logging.error(f"Error leyendo archivo M3U: {e}")
            return []
    
    def parse_url(self, url: str) -> List[M3UItem]:
        """Parsear M3U desde URL"""
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            return self.parse_content(response.text)
        except Exception as e:
            logging.error(f"Error descargando M3U: {e}")
            return []
    
    def parse_content(self, content: str) -> List[M3UItem]:
        """Parsear contenido M3U"""
        items = []
        lines = content.split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            if line.startswith('#EXTINF:'):
                # Parsear línea EXTINF
                item = self._parse_extinf_line(line)
                
                # Buscar URL en la siguiente línea
                if i + 1 < len(lines):
                    url_line = lines[i + 1].strip()
                    if url_line and not url_line.startswith('#'):
                        item.url = url_line
                        
                        # Detectar tipo de contenido
                        item.content_type = self._detect_content_type(item)
                        
                        # Si es Xtream Codes, intentar obtener más información
                        if self._is_xtream_url(url_line):
                            self._enhance_with_xtream_info(item)
                        
                        items.append(item)
                        i += 2
                        continue
            
            i += 1
        
        self.items = items
        logging.info(f"Parseados {len(items)} elementos M3U")
        return items
    
    def _parse_extinf_line(self, line: str) -> M3UItem:
        """Parsear línea EXTINF"""
        # Extraer duración
        duration_match = re.search(r'#EXTINF:(-?\d+)', line)
        duration = int(duration_match.group(1)) if duration_match else -1
        
        # Extraer atributos
        attributes = {}
        
        # tvg-id
        tvg_id_match = re.search(r'tvg-id="([^"]*)"', line)
        if tvg_id_match:
            attributes['tvg_id'] = tvg_id_match.group(1)
        
        # tvg-name
        tvg_name_match = re.search(r'tvg-name="([^"]*)"', line)
        if tvg_name_match:
            attributes['tvg_name'] = tvg_name_match.group(1)
        
        # tvg-logo
        logo_match = re.search(r'tvg-logo="([^"]*)"', line)
        logo = logo_match.group(1) if logo_match else ""
        
        # group-title
        group_match = re.search(r'group-title="([^"]*)"', line)
        group = group_match.group(1) if group_match else ""
        
        # Extraer nombre (después de la última coma)
        name_match = re.search(r',([^,]+)$', line)
        name = name_match.group(1).strip() if name_match else "Sin nombre"
        
        return M3UItem(
            name=name,
            url="",
            group=group,
            logo=logo,
            duration=duration,
            tvg_id=attributes.get('tvg_id', ''),
            tvg_name=attributes.get('tvg_name', ''),
            extra_info=attributes
        )
    
    def _detect_content_type(self, item: M3UItem) -> ContentType:
        """Detectar tipo de contenido"""
        name_lower = item.name.lower()
        group_lower = item.group.lower()
        url_lower = item.url.lower()
        
        # Detectar series
        series_indicators = ['s0', 'season', 'temporada', 'episode', 'episodio', 'ep.', 'x0']
        if any(indicator in name_lower for indicator in series_indicators):
            return ContentType.SERIES
        
        # Detectar películas
        movie_indicators = ['movie', 'film', 'pelicula', 'cinema']
        if any(indicator in group_lower for indicator in movie_indicators):
            return ContentType.MOVIE
        
        # Detectar por URL
        if 'series' in url_lower:
            return ContentType.SERIES
        elif 'movie' in url_lower:
            return ContentType.MOVIE
        elif 'live' in url_lower:
            return ContentType.LIVE
        
        return ContentType.UNKNOWN
    
    def _is_xtream_url(self, url: str) -> bool:
        """Verificar si es una URL de Xtream Codes"""
        xtream_patterns = [
            r'/player_api\.php',
            r'/series/',
            r'/movie/',
            r'/live/'
        ]
        
        return any(re.search(pattern, url) for pattern in xtream_patterns)
    
    def _enhance_with_xtream_info(self, item: M3UItem):
        """Mejorar información usando API Xtream Codes"""
        if not self.xtream_api:
            return
        
        try:
            # Extraer ID del contenido de la URL
            if item.content_type == ContentType.SERIES:
                series_id = self._extract_series_id(item.url)
                if series_id:
                    info = self.xtream_api.get_series_info(series_id)
                    if info and item.extra_info:
                        item.extra_info.update(info)
            
            elif item.content_type == ContentType.MOVIE:
                vod_id = self._extract_vod_id(item.url)
                if vod_id:
                    info = self.xtream_api.get_vod_info(vod_id)
                    if info and item.extra_info:
                        item.extra_info.update(info)
        
        except Exception as e:
            logging.error(f"Error mejorando info con Xtream: {e}")
    
    def _extract_series_id(self, url: str) -> Optional[str]:
        """Extraer ID de serie de la URL"""
        match = re.search(r'/series/(\d+)', url)
        return match.group(1) if match else None
    
    def _extract_vod_id(self, url: str) -> Optional[str]:
        """Extraer ID de VOD de la URL"""
        match = re.search(r'/movie/(\d+)', url)
        return match.group(1) if match else None
    
    def setup_xtream_api(self, base_url: str, username: str, password: str):
        """Configurar API Xtream Codes"""
        self.xtream_api = XtreamCodesAPI(base_url, username, password)
    
    def get_items_by_type(self, content_type: ContentType) -> List[M3UItem]:
        """Obtener elementos por tipo"""
        return [item for item in self.items if item.content_type == content_type]
    
    def get_items_by_group(self, group: str) -> List[M3UItem]:
        """Obtener elementos por grupo"""
        return [item for item in self.items if item.group == group]
    
    def search_items(self, query: str) -> List[M3UItem]:
        """Buscar elementos por nombre"""
        query_lower = query.lower()
        return [item for item in self.items 
                if query_lower in item.name.lower() or query_lower in item.group.lower()]
    
    def get_series_episodes(self, series_name: str) -> List[M3UItem]:
        """Obtener episodios de una serie"""
        series_items = []
        
        for item in self.items:
            if item.content_type == ContentType.SERIES:
                # Extraer nombre base de la serie
                base_name = self._extract_series_base_name(item.name)
                if base_name.lower() == series_name.lower():
                    series_items.append(item)
        
        # Ordenar por temporada y episodio
        return sorted(series_items, key=lambda x: self._get_episode_order(x.name))
    
    def _extract_series_base_name(self, full_name: str) -> str:
        """Extraer nombre base de serie"""
        # Remover indicadores de temporada/episodio
        patterns = [
            r'\s+S\d+E\d+.*',
            r'\s+\d+x\d+.*',
            r'\s+Season\s+\d+.*',
            r'\s+Temporada\s+\d+.*',
            r'\s+Episode\s+\d+.*',
            r'\s+Episodio\s+\d+.*'
        ]
        
        clean_name = full_name
        for pattern in patterns:
            clean_name = re.sub(pattern, '', clean_name, flags=re.IGNORECASE)
        
        return clean_name.strip()
    
    def _get_episode_order(self, episode_name: str) -> Tuple[int, int]:
        """Obtener orden de episodio (temporada, episodio)"""
        # Buscar patrón SxxExx
        match = re.search(r'S(\d+)E(\d+)', episode_name, re.IGNORECASE)
        if match:
            return (int(match.group(1)), int(match.group(2)))
        
        # Buscar patrón xxXxx
        match = re.search(r'(\d+)x(\d+)', episode_name)
        if match:
            return (int(match.group(1)), int(match.group(2)))
        
        return (0, 0)
    
    def get_statistics(self) -> Dict:
        """Obtener estadísticas del contenido"""
        stats = {
            'total': len(self.items),
            'movies': len(self.get_items_by_type(ContentType.MOVIE)),
            'series': len(self.get_items_by_type(ContentType.SERIES)),
            'live': len(self.get_items_by_type(ContentType.LIVE)),
            'unknown': len(self.get_items_by_type(ContentType.UNKNOWN)),
            'groups': len(set(item.group for item in self.items if item.group))
        }
        
        return stats
