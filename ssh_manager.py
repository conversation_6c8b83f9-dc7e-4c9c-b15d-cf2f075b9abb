import paramiko
import threading
import time
import os
import stat
import re
from datetime import datetime
from typing import List, Dict, Optional, Callable, Tuple
import logging
from dataclasses import dataclass
from enum import Enum

class FileType(Enum):
    FILE = "file"
    DIRECTORY = "directory"
    SYMLINK = "symlink"
    UNKNOWN = "unknown"

@dataclass
class RemoteFile:
    """Representa un archivo remoto"""
    name: str
    path: str
    size: int
    modified: datetime
    permissions: str
    file_type: FileType
    owner: str = ""
    group: str = ""

class SSHManager:
    """Gestión de conexiones SSH/SFTP"""
    
    def __init__(self):
        self.ssh_client = None
        self.sftp_client = None
        self.is_connected = False
        self.connection_params = {}
        self.current_path = "/"
        self.connection_lock = threading.Lock()
        
    def connect(self, hostname: str, username: str, password: str = "", 
                port: int = 22, key_file: str = "", timeout: int = 30) -> bool:
        """Establecer conexión SSH/SFTP"""
        try:
            with self.connection_lock:
                # Cerrar conexión existente
                self.disconnect()
                
                # Crear cliente SSH
                self.ssh_client = paramiko.SSHClient()
                self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                # Parámetros de conexión
                connect_params = {
                    'hostname': hostname,
                    'username': username,
                    'port': port,
                    'timeout': timeout,
                    'allow_agent': False,
                    'look_for_keys': False
                }
                
                # Autenticación
                if key_file and os.path.exists(key_file):
                    connect_params['key_filename'] = key_file
                elif password:
                    connect_params['password'] = password
                else:
                    raise ValueError("Debe proporcionar contraseña o archivo de clave")
                
                # Conectar
                self.ssh_client.connect(**connect_params)
                
                # Crear cliente SFTP
                self.sftp_client = self.ssh_client.open_sftp()
                
                # Guardar parámetros
                self.connection_params = connect_params
                self.is_connected = True
                
                # Establecer directorio inicial
                self.current_path = self.sftp_client.getcwd() or "/"
                
                logging.info(f"Conectado exitosamente a {hostname}:{port}")
                return True
                
        except Exception as e:
            logging.error(f"Error conectando SSH: {e}")
            self.disconnect()
            return False
    
    def disconnect(self):
        """Cerrar conexión SSH/SFTP"""
        try:
            with self.connection_lock:
                if self.sftp_client:
                    self.sftp_client.close()
                    self.sftp_client = None
                    
                if self.ssh_client:
                    self.ssh_client.close()
                    self.ssh_client = None
                    
                self.is_connected = False
                logging.info("Desconectado de SSH")
                
        except Exception as e:
            logging.error(f"Error desconectando SSH: {e}")
    
    def test_connection(self) -> bool:
        """Probar si la conexión está activa"""
        try:
            if not self.is_connected or not self.ssh_client:
                return False
                
            # Enviar comando simple
            stdin, stdout, stderr = self.ssh_client.exec_command('echo "test"', timeout=5)
            result = stdout.read().decode().strip()
            return result == "test"
            
        except Exception as e:
            logging.error(f"Error probando conexión: {e}")
            return False
    
    def reconnect(self) -> bool:
        """Reconectar usando los últimos parámetros"""
        if not self.connection_params:
            return False
            
        return self.connect(**self.connection_params)
    
    def list_directory(self, path: Optional[str] = None) -> List[RemoteFile]:
        """Listar contenido de directorio"""
        if not self.is_connected or not self.sftp_client:
            return []
            
        try:
            target_path = path or self.current_path
            
            # Listar archivos
            file_attrs = self.sftp_client.listdir_attr(target_path)
            files = []
            
            for attr in file_attrs:
                file_type = self._get_file_type(attr)
                
                # Construir ruta completa
                full_path = os.path.join(target_path, attr.filename).replace('\\', '/')
                
                # Convertir timestamp a datetime
                modified = datetime.fromtimestamp(attr.st_mtime) if attr.st_mtime else datetime.now()
                
                # Formatear permisos
                permissions = self._format_permissions(attr.st_mode) if attr.st_mode else "----------"
                
                files.append(RemoteFile(
                    name=attr.filename,
                    path=full_path,
                    size=attr.st_size or 0,
                    modified=modified,
                    permissions=permissions,
                    file_type=file_type,
                    owner=str(attr.st_uid) if attr.st_uid else "",
                    group=str(attr.st_gid) if attr.st_gid else ""
                ))
            
            # Ordenar: directorios primero, luego archivos
            files.sort(key=lambda x: (x.file_type != FileType.DIRECTORY, x.name.lower()))
            
            return files
            
        except Exception as e:
            logging.error(f"Error listando directorio {path}: {e}")
            return []
    
    def change_directory(self, path: str) -> bool:
        """Cambiar directorio actual"""
        if not self.is_connected or not self.sftp_client:
            return False
            
        try:
            # Verificar si existe y es directorio
            stat_info = self.sftp_client.stat(path)
            if stat_info.st_mode and stat.S_ISDIR(stat_info.st_mode):
                self.current_path = path
                return True
            else:
                return False
                
        except Exception as e:
            logging.error(f"Error cambiando directorio a {path}: {e}")
            return False
    
    def create_directory(self, path: str) -> bool:
        """Crear directorio"""
        if not self.is_connected or not self.sftp_client:
            return False
            
        try:
            self.sftp_client.mkdir(path)
            logging.info(f"Directorio creado: {path}")
            return True
            
        except Exception as e:
            logging.error(f"Error creando directorio {path}: {e}")
            return False
    
    def delete_file(self, path: str) -> bool:
        """Eliminar archivo"""
        if not self.is_connected or not self.sftp_client:
            return False
            
        try:
            self.sftp_client.remove(path)
            logging.info(f"Archivo eliminado: {path}")
            return True
            
        except Exception as e:
            logging.error(f"Error eliminando archivo {path}: {e}")
            return False
    
    def delete_directory(self, path: str) -> bool:
        """Eliminar directorio"""
        if not self.is_connected or not self.sftp_client:
            return False
            
        try:
            self.sftp_client.rmdir(path)
            logging.info(f"Directorio eliminado: {path}")
            return True
            
        except Exception as e:
            logging.error(f"Error eliminando directorio {path}: {e}")
            return False
    
    def upload_file(self, local_path: str, remote_path: str, 
                   progress_callback: Optional[Callable] = None) -> bool:
        """Subir archivo al servidor"""
        if not self.is_connected or not self.sftp_client:
            return False
            
        try:
            self.sftp_client.put(local_path, remote_path, callback=progress_callback)
            logging.info(f"Archivo subido: {local_path} -> {remote_path}")
            return True
            
        except Exception as e:
            logging.error(f"Error subiendo archivo {local_path}: {e}")
            return False
    
    def download_file(self, remote_path: str, local_path: str, 
                     progress_callback: Optional[Callable] = None) -> bool:
        """Descargar archivo del servidor"""
        if not self.is_connected or not self.sftp_client:
            return False
            
        try:
            self.sftp_client.get(remote_path, local_path, callback=progress_callback)
            logging.info(f"Archivo descargado: {remote_path} -> {local_path}")
            return True
            
        except Exception as e:
            logging.error(f"Error descargando archivo {remote_path}: {e}")
            return False
    
    def execute_command(self, command: str, timeout: int = 30) -> Tuple[str, str, int]:
        """Ejecutar comando SSH"""
        if not self.is_connected or not self.ssh_client:
            return "", "No conectado", 1
            
        try:
            stdin, stdout, stderr = self.ssh_client.exec_command(command, timeout=timeout)
            
            # Leer salida
            stdout_data = stdout.read().decode('utf-8', errors='ignore')
            stderr_data = stderr.read().decode('utf-8', errors='ignore')
            exit_code = stdout.channel.recv_exit_status()
            
            return stdout_data, stderr_data, exit_code
            
        except Exception as e:
            logging.error(f"Error ejecutando comando '{command}': {e}")
            return "", str(e), 1
    
    def get_file_info(self, path: str) -> Optional[RemoteFile]:
        """Obtener información de archivo"""
        if not self.is_connected or not self.sftp_client:
            return None
            
        try:
            attr = self.sftp_client.stat(path)
            file_type = self._get_file_type(attr)
            modified = datetime.fromtimestamp(attr.st_mtime) if attr.st_mtime else datetime.now()
            permissions = self._format_permissions(attr.st_mode) if attr.st_mode else "----------"
            
            return RemoteFile(
                name=os.path.basename(path),
                path=path,
                size=attr.st_size or 0,
                modified=modified,
                permissions=permissions,
                file_type=file_type,
                owner=str(attr.st_uid) if attr.st_uid else "",
                group=str(attr.st_gid) if attr.st_gid else ""
            )
            
        except Exception as e:
            logging.error(f"Error obteniendo info de archivo {path}: {e}")
            return None
    
    def _get_file_type(self, attr) -> FileType:
        """Determinar tipo de archivo"""
        if not attr.st_mode:
            return FileType.UNKNOWN
            
        if stat.S_ISDIR(attr.st_mode):
            return FileType.DIRECTORY
        elif stat.S_ISLNK(attr.st_mode):
            return FileType.SYMLINK
        elif stat.S_ISREG(attr.st_mode):
            return FileType.FILE
        else:
            return FileType.UNKNOWN
    
    def _format_permissions(self, mode: int) -> str:
        """Formatear permisos en formato legible"""
        permissions = ""
        
        # Tipo de archivo
        if stat.S_ISDIR(mode):
            permissions += "d"
        elif stat.S_ISLNK(mode):
            permissions += "l"
        else:
            permissions += "-"
        
        # Permisos del propietario
        permissions += "r" if mode & stat.S_IRUSR else "-"
        permissions += "w" if mode & stat.S_IWUSR else "-"
        permissions += "x" if mode & stat.S_IXUSR else "-"
        
        # Permisos del grupo
        permissions += "r" if mode & stat.S_IRGRP else "-"
        permissions += "w" if mode & stat.S_IWGRP else "-"
        permissions += "x" if mode & stat.S_IXGRP else "-"
        
        # Permisos de otros
        permissions += "r" if mode & stat.S_IROTH else "-"
        permissions += "w" if mode & stat.S_IWOTH else "-"
        permissions += "x" if mode & stat.S_IXOTH else "-"
        
        return permissions

class RemoteDownloader:
    """Gestor de descargas remotas usando wget"""
    
    def __init__(self, ssh_manager: SSHManager):
        self.ssh_manager = ssh_manager
        self.active_downloads = {}
        self.download_lock = threading.Lock()
    def download_file(self, url: str, remote_path: str, filename: Optional[str] = None,
                     progress_callback: Optional[Callable] = None) -> bool:
        """Descargar archivo usando wget en el servidor"""
        if not self.ssh_manager.is_connected:
            return False
            
        try:
            # Generar nombre de archivo si no se proporciona
            if not filename:
                filename = self._generate_filename(url)
            
            # Ruta completa de destino
            full_path = os.path.join(remote_path, filename).replace('\\', '/')
            
            # Comando wget con opciones
            wget_cmd = f'wget -c -t 3 -T 30 --progress=bar:force "{url}" -O "{full_path}"'
            
            # Ejecutar descarga
            if progress_callback:
                return self._download_with_progress(wget_cmd, progress_callback)
            else:
                stdout, stderr, exit_code = self.ssh_manager.execute_command(wget_cmd, timeout=3600)
                return exit_code == 0
                
        except Exception as e:
            logging.error(f"Error descargando {url}: {e}")
            return False
    
    def download_multiple(self, download_list: List[Dict], remote_path: str, 
                         progress_callback: Optional[Callable] = None) -> Dict[str, bool]:
        """Descargar múltiples archivos"""
        results = {}
        
        for i, download_info in enumerate(download_list):
            url = download_info['url']
            filename = download_info.get('filename')
            
            if progress_callback:
                progress_callback(f"Descargando {i+1}/{len(download_list)}: {filename or url}")
            
            success = self.download_file(url, remote_path, filename, progress_callback)
            results[url] = success
            
            if not success:
                logging.error(f"Falló descarga: {url}")
        
        return results
    
    def _download_with_progress(self, command: str, progress_callback: Callable) -> bool:
        """Ejecutar descarga con monitoreo de progreso"""
        try:
            if not self.ssh_manager.ssh_client:
                return False
                
            # Ejecutar comando en background
            stdin, stdout, stderr = self.ssh_manager.ssh_client.exec_command(command)
            
            # Monitorear progreso
            while not stdout.channel.exit_status_ready():
                if stdout.channel.recv_ready():
                    output = stdout.channel.recv(1024).decode('utf-8', errors='ignore')
                    
                    # Parsear progreso de wget
                    progress = self._parse_wget_progress(output)
                    if progress and progress_callback:
                        progress_callback(f"Descargando... {progress}%")
                
                time.sleep(0.5)
            
            # Obtener código de salida
            exit_code = stdout.channel.recv_exit_status()
            return exit_code == 0
            
        except Exception as e:
            logging.error(f"Error monitoreando descarga: {e}")
            return False
    
    def _parse_wget_progress(self, output: str) -> Optional[int]:
        """Parsear progreso de wget"""
        try:
            # Buscar porcentaje en salida de wget
            import re
            match = re.search(r'(\d+)%', output)
            if match:
                return int(match.group(1))
        except:
            pass
        
        return None
    
    def _generate_filename(self, url: str) -> str:
        """Generar nombre de archivo desde URL"""
        try:
            # Extraer nombre del archivo de la URL
            from urllib.parse import urlparse, unquote
            parsed = urlparse(url)
            filename = os.path.basename(unquote(parsed.path))
            
            # Si no hay extensión, detectar por contenido
            if not os.path.splitext(filename)[1]:
                # Extensiones comunes para video
                filename += ".mp4"
            
            # Limpiar caracteres especiales
            filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
            
            return filename
            
        except Exception as e:
            logging.error(f"Error generando nombre de archivo: {e}")
            return f"download_{int(time.time())}.mp4"
    
    def cancel_download(self, url: str) -> bool:
        """Cancelar descarga activa"""
        try:
            # Encontrar proceso wget
            find_cmd = f'pgrep -f "wget.*{url}"'
            stdout, stderr, exit_code = self.ssh_manager.execute_command(find_cmd)
            
            if exit_code == 0 and stdout.strip():
                pid = stdout.strip()
                kill_cmd = f'kill -TERM {pid}'
                stdout, stderr, exit_code = self.ssh_manager.execute_command(kill_cmd)
                return exit_code == 0
            
            return False
            
        except Exception as e:
            logging.error(f"Error cancelando descarga: {e}")
            return False
    
    def get_download_status(self, remote_path: str) -> Dict:
        """Obtener estado de descargas en progreso"""
        try:
            # Buscar procesos wget activos
            cmd = 'ps aux | grep wget | grep -v grep'
            stdout, stderr, exit_code = self.ssh_manager.execute_command(cmd)
            
            active_downloads = []
            if exit_code == 0 and stdout.strip():
                for line in stdout.strip().split('\n'):
                    if 'wget' in line:
                        active_downloads.append(line)
            
            return {
                'active_count': len(active_downloads),
                'processes': active_downloads
            }
            
        except Exception as e:
            logging.error(f"Error obteniendo estado de descargas: {e}")
            return {'active_count': 0, 'processes': []}
