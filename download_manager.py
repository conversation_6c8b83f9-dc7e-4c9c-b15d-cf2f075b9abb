import threading
import queue
import time
import os
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
import logging
from dataclasses import dataclass, field
from enum import Enum
import json

class DownloadStatus(Enum):
    PENDING = "pending"
    DOWNLOADING = "downloading"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

@dataclass
class DownloadTask:
    """Tarea de descarga"""
    id: str
    url: str
    filename: str
    remote_path: str
    content_type: str = "unknown"
    size: int = 0
    progress: float = 0.0
    status: DownloadStatus = DownloadStatus.PENDING
    error_message: str = ""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.id:
            self.id = f"download_{int(time.time() * 1000)}"

class DownloadManager:
    """Gestor de descargas con cola y progreso"""
    
    def __init__(self, ssh_manager=None, max_concurrent_downloads: int = 3):
        self.ssh_manager = ssh_manager
        self.max_concurrent_downloads = max_concurrent_downloads
        
        # Colas y estado
        self.download_queue = queue.Queue()
        self.active_downloads: Dict[str, DownloadTask] = {}
        self.completed_downloads: List[DownloadTask] = []
        self.failed_downloads: List[DownloadTask] = []
        
        # Control de threads
        self.download_threads: List[threading.Thread] = []
        self.is_running = False
        self.manager_thread = None
        
        # Callbacks
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None
        
        # Estadísticas
        self.stats = {
            'total_downloads': 0,
            'completed_downloads': 0,
            'failed_downloads': 0,
            'cancelled_downloads': 0,
            'total_size': 0,
            'downloaded_size': 0,
            'average_speed': 0.0,
            'start_time': None,
            'end_time': None
        }
        
        # Lock para thread safety
        self.lock = threading.Lock()
        
        logging.info("Gestor de descargas inicializado")
    
    def set_ssh_manager(self, ssh_manager):
        """Establecer gestor SSH"""
        self.ssh_manager = ssh_manager
    
    def set_progress_callback(self, callback: Callable):
        """Establecer callback de progreso"""
        self.progress_callback = callback
    
    def set_status_callback(self, callback: Callable):
        """Establecer callback de estado"""
        self.status_callback = callback
    
    def add_download(self, url: str, filename: str, remote_path: str, 
                    content_type: str = "unknown", metadata: Optional[Dict] = None) -> str:
        """Agregar descarga a la cola"""
        try:
            # Crear tarea
            task = DownloadTask(
                id=f"download_{int(time.time() * 1000)}_{len(self.active_downloads)}",
                url=url,
                filename=filename,
                remote_path=remote_path,
                content_type=content_type,
                metadata=metadata or {}
            )
            
            # Agregar a cola
            with self.lock:
                self.download_queue.put(task)
                self.stats['total_downloads'] += 1
            
            logging.info(f"Descarga agregada: {filename}")
            
            # Notificar cambio de estado
            if self.status_callback:
                self.status_callback("download_added", task)
            
            return task.id
            
        except Exception as e:
            logging.error(f"Error agregando descarga: {e}")
            return ""
    
    def add_bulk_downloads(self, downloads: List[Dict]) -> List[str]:
        """Agregar múltiples descargas"""
        task_ids = []
        
        for download in downloads:
            task_id = self.add_download(
                url=download['url'],
                filename=download['filename'],
                remote_path=download['remote_path'],
                content_type=download.get('content_type', 'unknown'),
                metadata=download.get('metadata', {})
            )
            if task_id:
                task_ids.append(task_id)
        
        logging.info(f"Agregadas {len(task_ids)} descargas masivas")
        return task_ids
    
    def start_downloads(self):
        """Iniciar el gestor de descargas"""
        if self.is_running:
            return
        
        self.is_running = True
        self.stats['start_time'] = datetime.now()
        
        # Iniciar thread manager
        self.manager_thread = threading.Thread(target=self._download_manager, daemon=True)
        self.manager_thread.start()
        
        logging.info("Gestor de descargas iniciado")
        
        if self.status_callback:
            self.status_callback("manager_started", None)
    
    def stop_downloads(self):
        """Detener el gestor de descargas"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancelar descargas activas
        with self.lock:
            for task in self.active_downloads.values():
                task.status = DownloadStatus.CANCELLED
                self.stats['cancelled_downloads'] += 1
        
        # Esperar que terminen los threads
        for thread in self.download_threads:
            if thread.is_alive():
                thread.join(timeout=5.0)
        
        self.download_threads.clear()
        self.stats['end_time'] = datetime.now()
        
        logging.info("Gestor de descargas detenido")
        
        if self.status_callback:
            self.status_callback("manager_stopped", None)
    
    def pause_download(self, task_id: str) -> bool:
        """Pausar descarga específica"""
        with self.lock:
            if task_id in self.active_downloads:
                task = self.active_downloads[task_id]
                task.status = DownloadStatus.PAUSED
                return True
        return False
    
    def resume_download(self, task_id: str) -> bool:
        """Reanudar descarga específica"""
        with self.lock:
            if task_id in self.active_downloads:
                task = self.active_downloads[task_id]
                if task.status == DownloadStatus.PAUSED:
                    task.status = DownloadStatus.PENDING
                    return True
        return False
    
    def cancel_download(self, task_id: str) -> bool:
        """Cancelar descarga específica"""
        with self.lock:
            if task_id in self.active_downloads:
                task = self.active_downloads[task_id]
                task.status = DownloadStatus.CANCELLED
                self.stats['cancelled_downloads'] += 1
                return True
        return False
    
    def clear_queue(self):
        """Limpiar cola de descargas"""
        with self.lock:
            # Limpiar cola
            while not self.download_queue.empty():
                try:
                    self.download_queue.get_nowait()
                except queue.Empty:
                    break
            
            # Limpiar listas
            self.completed_downloads.clear()
            self.failed_downloads.clear()
            
            # Resetear estadísticas
            self.stats = {
                'total_downloads': len(self.active_downloads),
                'completed_downloads': 0,
                'failed_downloads': 0,
                'cancelled_downloads': 0,
                'total_size': 0,
                'downloaded_size': 0,
                'average_speed': 0.0,
                'start_time': self.stats.get('start_time'),
                'end_time': None
            }
        
        logging.info("Cola de descargas limpiada")
        
        if self.status_callback:
            self.status_callback("queue_cleared", None)
    
    def get_queue_status(self) -> Dict:
        """Obtener estado de la cola"""
        with self.lock:
            return {
                'pending': self.download_queue.qsize(),
                'active': len(self.active_downloads),
                'completed': len(self.completed_downloads),
                'failed': len(self.failed_downloads),
                'total': self.stats['total_downloads'],
                'is_running': self.is_running
            }
    
    def get_download_stats(self) -> Dict:
        """Obtener estadísticas de descarga"""
        with self.lock:
            # Calcular velocidad promedio
            if self.stats['start_time']:
                elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
                if elapsed > 0:
                    self.stats['average_speed'] = self.stats['downloaded_size'] / elapsed
            
            return self.stats.copy()
    
    def get_active_downloads(self) -> List[DownloadTask]:
        """Obtener descargas activas"""
        with self.lock:
            return list(self.active_downloads.values())
    
    def get_download_by_id(self, task_id: str) -> Optional[DownloadTask]:
        """Obtener descarga por ID"""
        with self.lock:
            return self.active_downloads.get(task_id)
    
    def _download_manager(self):
        """Thread principal del gestor"""
        while self.is_running:
            try:
                # Verificar si hay espacio para más descargas
                if len(self.active_downloads) < self.max_concurrent_downloads:
                    try:
                        # Obtener siguiente tarea
                        task = self.download_queue.get(timeout=1.0)
                        
                        # Iniciar descarga
                        self._start_download_thread(task)
                        
                    except queue.Empty:
                        pass
                
                # Limpiar threads terminados
                self._cleanup_threads()
                
                # Pequeña pausa
                time.sleep(0.5)
                
            except Exception as e:
                logging.error(f"Error en gestor de descargas: {e}")
                time.sleep(1.0)
    
    def _start_download_thread(self, task: DownloadTask):
        """Iniciar thread de descarga"""
        with self.lock:
            self.active_downloads[task.id] = task
            task.status = DownloadStatus.DOWNLOADING
            task.start_time = datetime.now()
        
        # Crear y iniciar thread
        thread = threading.Thread(
            target=self._download_worker,
            args=(task,),
            daemon=True
        )
        thread.start()
        self.download_threads.append(thread)
        
        logging.info(f"Iniciando descarga: {task.filename}")
        
        if self.status_callback:
            self.status_callback("download_started", task)
    
    def _download_worker(self, task: DownloadTask):
        """Worker de descarga"""
        try:
            # Verificar SSH manager
            if not self.ssh_manager or not self.ssh_manager.is_connected:
                raise Exception("SSH no conectado")
            
            # Preparar comando wget
            remote_file_path = os.path.join(task.remote_path, task.filename).replace('\\', '/')
            
            # Comando wget mejorado
            wget_cmd = (
                f'wget -c -t {task.max_retries} -T 30 '
                f'--progress=bar:force:noscroll '
                f'--tries={task.max_retries} '
                f'"{task.url}" -O "{remote_file_path}"'
            )
            
            # Ejecutar descarga
            success = self._execute_download(task, wget_cmd)
            
            # Actualizar estado
            with self.lock:
                task.end_time = datetime.now()
                
                if success:
                    task.status = DownloadStatus.COMPLETED
                    task.progress = 100.0
                    self.completed_downloads.append(task)
                    self.stats['completed_downloads'] += 1
                    
                    logging.info(f"Descarga completada: {task.filename}")
                    
                    if self.status_callback:
                        self.status_callback("download_completed", task)
                else:
                    task.status = DownloadStatus.FAILED
                    self.failed_downloads.append(task)
                    self.stats['failed_downloads'] += 1
                    
                    logging.error(f"Descarga falló: {task.filename} - {task.error_message}")
                    
                    if self.status_callback:
                        self.status_callback("download_failed", task)
                
                # Remover de activos
                if task.id in self.active_downloads:
                    del self.active_downloads[task.id]
        
        except Exception as e:
            logging.error(f"Error en worker de descarga: {e}")
            
            with self.lock:
                task.status = DownloadStatus.FAILED
                task.error_message = str(e)
                task.end_time = datetime.now()
                self.failed_downloads.append(task)
                self.stats['failed_downloads'] += 1
                
                if task.id in self.active_downloads:
                    del self.active_downloads[task.id]
            
            if self.status_callback:
                self.status_callback("download_failed", task)
    
    def _execute_download(self, task: DownloadTask, command: str) -> bool:
        """Ejecutar comando de descarga"""
        try:
            # Verificar SSH manager
            if not self.ssh_manager or not self.ssh_manager.is_connected:
                raise Exception("SSH no conectado")
            
            # Ejecutar comando
            stdout, stderr, exit_code = self.ssh_manager.execute_command(command, timeout=3600)
            
            # Verificar resultado
            if exit_code == 0:
                return True
            else:
                task.error_message = stderr or "Error desconocido"
                return False
                
        except Exception as e:
            task.error_message = str(e)
            return False
    
    def _cleanup_threads(self):
        """Limpiar threads terminados"""
        active_threads = []
        for thread in self.download_threads:
            if thread.is_alive():
                active_threads.append(thread)
        
        self.download_threads = active_threads
    
    def save_state(self, filepath: str):
        """Guardar estado del gestor"""
        try:
            state = {
                'stats': self.stats,
                'completed_downloads': [self._task_to_dict(task) for task in self.completed_downloads],
                'failed_downloads': [self._task_to_dict(task) for task in self.failed_downloads],
                'active_downloads': [self._task_to_dict(task) for task in self.active_downloads.values()],
                'timestamp': datetime.now().isoformat()
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2, ensure_ascii=False)
            
            logging.info(f"Estado guardado en: {filepath}")
            
        except Exception as e:
            logging.error(f"Error guardando estado: {e}")
    
    def load_state(self, filepath: str):
        """Cargar estado del gestor"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            # Restaurar estadísticas
            self.stats.update(state.get('stats', {}))
            
            # Restaurar listas
            self.completed_downloads = [self._dict_to_task(task_dict) for task_dict in state.get('completed_downloads', [])]
            self.failed_downloads = [self._dict_to_task(task_dict) for task_dict in state.get('failed_downloads', [])]
            
            logging.info(f"Estado cargado desde: {filepath}")
            
        except Exception as e:
            logging.error(f"Error cargando estado: {e}")
    
    def _task_to_dict(self, task: DownloadTask) -> Dict:
        """Convertir tarea a diccionario"""
        return {
            'id': task.id,
            'url': task.url,
            'filename': task.filename,
            'remote_path': task.remote_path,
            'content_type': task.content_type,
            'size': task.size,
            'progress': task.progress,
            'status': task.status.value,
            'error_message': task.error_message,
            'start_time': task.start_time.isoformat() if task.start_time else None,
            'end_time': task.end_time.isoformat() if task.end_time else None,
            'retry_count': task.retry_count,
            'max_retries': task.max_retries,
            'metadata': task.metadata
        }
    
    def _dict_to_task(self, task_dict: Dict) -> DownloadTask:
        """Convertir diccionario a tarea"""
        return DownloadTask(
            id=task_dict['id'],
            url=task_dict['url'],
            filename=task_dict['filename'],
            remote_path=task_dict['remote_path'],
            content_type=task_dict['content_type'],
            size=task_dict['size'],
            progress=task_dict['progress'],
            status=DownloadStatus(task_dict['status']),
            error_message=task_dict['error_message'],
            start_time=datetime.fromisoformat(task_dict['start_time']) if task_dict['start_time'] else None,
            end_time=datetime.fromisoformat(task_dict['end_time']) if task_dict['end_time'] else None,
            retry_count=task_dict['retry_count'],
            max_retries=task_dict['max_retries'],
            metadata=task_dict['metadata']
        )

class SeriesDownloadManager:
    """Gestor especializado para series"""
    
    def __init__(self, download_manager: DownloadManager):
        self.download_manager = download_manager
        self.series_cache = {}
    
    def download_complete_series(self, series_episodes: List[Dict], remote_path: str) -> List[str]:
        """Descargar serie completa"""
        downloads = []
        
        for episode in series_episodes:
            # Generar nombre de archivo mejorado
            filename = self._generate_episode_filename(episode)
            
            downloads.append({
                'url': episode['url'],
                'filename': filename,
                'remote_path': remote_path,
                'content_type': 'series',
                'metadata': {
                    'series_name': episode.get('series_name', ''),
                    'season': episode.get('season', ''),
                    'episode': episode.get('episode', ''),
                    'title': episode.get('title', '')
                }
            })
        
        return self.download_manager.add_bulk_downloads(downloads)
    
    def download_season(self, series_episodes: List[Dict], season: str, remote_path: str) -> List[str]:
        """Descargar temporada específica"""
        season_episodes = [ep for ep in series_episodes if ep.get('season') == season]
        return self.download_complete_series(season_episodes, remote_path)
    
    def _generate_episode_filename(self, episode: Dict) -> str:
        """Generar nombre de archivo para episodio"""
        try:
            series_name = episode.get('series_name', 'Serie')
            season = episode.get('season', '1')
            episode_num = episode.get('episode', '1')
            title = episode.get('title', '')
            
            # Limpiar nombres
            series_name = self._clean_filename(series_name)
            title = self._clean_filename(title)
            
            # Formato: Series.Name.S01E01.Episode.Title.mp4
            filename = f"{series_name}.S{season.zfill(2)}E{episode_num.zfill(2)}"
            
            if title:
                filename += f".{title}"
            
            filename += ".mp4"
            
            return filename
            
        except Exception as e:
            logging.error(f"Error generando nombre de episodio: {e}")
            return f"episode_{int(time.time())}.mp4"
    
    def _clean_filename(self, filename: str) -> str:
        """Limpiar nombre de archivo"""
        # Remover caracteres especiales
        import re
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        filename = re.sub(r'\s+', '.', filename.strip())
        return filename
